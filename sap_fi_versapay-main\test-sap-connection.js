#!/usr/bin/env node
/**
 * SAP RFC Connection Test Script
 * 
 * This script tests the SAP RFC connection after SDK installation.
 * Run this script to verify your SAP connectivity before starting the main application.
 * 
 * Usage: node test-sap-connection.js
 */

import "dotenv/config";

// Test if node-rfc is available
let noderfc;
try {
    noderfc = await import("node-rfc");
    console.log("✅ node-rfc module loaded successfully");
} catch (error) {
    console.error("❌ Failed to load node-rfc module:", error.message);
    console.log("\n📋 Troubleshooting steps:");
    console.log("1. Ensure SAP NetWeaver RFC SDK is installed");
    console.log("2. Set SAPNWRFC_HOME environment variable");
    console.log("3. Add SAP RFC SDK lib directory to PATH");
    console.log("4. Run 'npm install' to rebuild node-rfc");
    process.exit(1);
}

// SAP connection parameters from environment variables
const connectionParams = {
    ashost: process.env.ASHOST,
    sysnr: process.env.SYSNR,
    client: process.env.CLIENT,
    user: process.env.SAPUSER,
    passwd: process.env.SAPPSW,
    lang: process.env.LANGU || 'EN'
};

// Validate required parameters
const requiredParams = ['ashost', 'sysnr', 'client', 'user', 'passwd'];
const missingParams = requiredParams.filter(param => !connectionParams[param]);

if (missingParams.length > 0) {
    console.error("❌ Missing required SAP connection parameters:");
    missingParams.forEach(param => {
        console.error(`   - ${param.toUpperCase()}`);
    });
    console.log("\n📝 Please set these environment variables in your .env file");
    process.exit(1);
}

console.log("🔧 SAP Connection Parameters:");
console.log(`   Host: ${connectionParams.ashost}`);
console.log(`   System: ${connectionParams.sysnr}`);
console.log(`   Client: ${connectionParams.client}`);
console.log(`   User: ${connectionParams.user}`);
console.log(`   Language: ${connectionParams.lang}`);

async function testSAPConnection() {
    const client = new noderfc.default.Client(connectionParams);
    
    try {
        console.log("\n🔌 Attempting SAP RFC connection...");
        await client.open();
        console.log("✅ SAP RFC connection established successfully!");
        
        // Test basic system information
        console.log("\n📊 Testing RFC call - Getting system information...");
        const systemInfo = await client.call("RFC_SYSTEM_INFO");
        
        console.log("✅ RFC call successful!");
        console.log("📋 SAP System Information:");
        console.log(`   System ID: ${systemInfo.RFCSI_SYSID || 'N/A'}`);
        console.log(`   Release: ${systemInfo.RFCSI_RELEASE || 'N/A'}`);
        console.log(`   Host: ${systemInfo.RFCSI_HOST || 'N/A'}`);
        console.log(`   Database: ${systemInfo.RFCSI_DBSYS || 'N/A'}`);
        
        // Test table read (optional)
        try {
            console.log("\n📋 Testing table read - T000 (Client table)...");
            const clientData = await client.call("RFC_READ_TABLE", {
                QUERY_TABLE: "T000",
                ROWCOUNT: 5,
                FIELDS: [
                    { FIELDNAME: "MANDT" },
                    { FIELDNAME: "MTEXT" }
                ]
            });
            
            if (clientData.DATA && clientData.DATA.length > 0) {
                console.log("✅ Table read successful!");
                console.log("📊 Sample client data:");
                clientData.DATA.slice(0, 3).forEach((row, index) => {
                    console.log(`   ${index + 1}. ${row.WA}`);
                });
            }
        } catch (tableError) {
            console.log("⚠️  Table read test failed (this may be due to authorization):", tableError.message);
        }
        
        await client.close();
        console.log("\n🎉 SAP RFC connection test completed successfully!");
        console.log("✅ Your application should be able to connect to SAP");
        
    } catch (error) {
        console.error("\n❌ SAP RFC connection failed:");
        console.error(`   Error: ${error.message}`);
        
        // Provide specific troubleshooting based on error type
        if (error.message.includes("COMMUNICATION_FAILURE")) {
            console.log("\n🔍 Troubleshooting - Network/Communication Issues:");
            console.log("1. Check if SAP server is accessible");
            console.log("2. Verify ASHOST and SYSNR parameters");
            console.log("3. Check firewall settings");
            console.log("4. Test network connectivity: telnet <ASHOST> 33<SYSNR>");
        } else if (error.message.includes("LOGON_FAILURE")) {
            console.log("\n🔍 Troubleshooting - Authentication Issues:");
            console.log("1. Verify username and password");
            console.log("2. Check if user is locked in SAP");
            console.log("3. Ensure user has RFC authorization");
            console.log("4. Verify client number is correct");
        } else if (error.message.includes("RFC_ERROR_SYSTEM_FAILURE")) {
            console.log("\n🔍 Troubleshooting - SAP System Issues:");
            console.log("1. Check SAP system status");
            console.log("2. Verify RFC service is running");
            console.log("3. Check SAP system logs");
            console.log("4. Contact SAP Basis team");
        }
        
        process.exit(1);
    }
}

// Test environment variables
console.log("🧪 SAP RFC Connection Test");
console.log("=" .repeat(50));

// Run the test
testSAPConnection().catch(error => {
    console.error("💥 Unexpected error:", error);
    process.exit(1);
});
