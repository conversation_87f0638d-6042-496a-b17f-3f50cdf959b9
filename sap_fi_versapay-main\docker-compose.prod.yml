version: "3.9"

services:
  # Source (PIPO): <PERSON><PERSON><PERSON><PERSON>, Sentric
  # Versapay Mapping
  # Data Consumer and Posting to SAP-API
  consumer_versapay:
    container_name: consumer_versapay
    build: .
    image: consumer_versapay
    restart: always
    deploy:
      resources:
        limits:
          cpus: '0.50'
          memory: 1G
        reservations:
          cpus: '0.25'
          memory: 0.5G
    env_file:
      - "./aconfig/config.production.env"
    environment:
      - ASHOST=ud3-sv-sapec3.matw.matthewsintl.com
      - SYSNR=00
      - CLIENT=300
      - LANGU=EN
      - SYSTEM=NP1
      - RFC_TRACE=0
      - KAFKA_BROKER=dc6-sv-c3mft02.amer.schawk.com:9092,dc6-sv-c3mft02.amer.schawk.com:9093
      - KAFKA_CLIENTID=SAP.GL.POSTING
      - KAFKA_GROUPID=SAP-GL-POST-VERSAPAY
      - KAFKA_TOPIC_RECEIVED=GL.SAPGL.RECEIVED
      - KAFKA_TOPIC_VERSAPAY=versapay_payment_prd
      - KAFKAJS_NO_PARTITIONER_WARNING=1
      - MONGODB_HOST=dc6-sv-elk01.amer.schawk.com:5311
      - VERSAPAY_ENDPOINT=https://secure.versapay.com
      - VERSAPAY_WHOAMI=PXh-2jbAhxrs3aiynrdr