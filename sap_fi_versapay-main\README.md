# SAP FI Versapay Integration

A Node.js microservice that integrates SAP Financial (FI) module with Versapay payment processing system using Kafka messaging and MongoDB for data persistence.

## 🏗️ Architecture Overview

This application processes payment data from Versapay and creates corresponding SAP Financial documents through the following workflow:

1. **Kafka Consumer** - Consumes payment messages from Versapay topic
2. **Versapay API Integration** - Fetches detailed payment information including child payments
3. **SAP RFC Integration** - Queries SAP for customer data, GL accounts, and profit centers
4. **Data Mapping & Validation** - Maps Versapay data to SAP FI document structure
5. **Document Generation** - Creates SAP FI documents and publishes to SAP GL topic
6. **MongoDB Persistence** - Stores processing data for audit and retry purposes

## 🚀 Quick Start

### Prerequisites

- Node.js 18.20.4 or higher
- Docker and Docker Compose
- Access to SAP system with RFC connectivity
- Versapay API credentials
- Kafka cluster access
- MongoDB instance

### Environment Setup

1. **Clone the repository**
   ```bash
   git clone https://gitlab.com/matw_integration/sap/sap_fi_versapay.git
   cd sap_fi_versapay
   ```

2. **Configure environment variables**
   ```bash
   cp .env.example .env
   # Edit .env with your actual configuration values
   ```

3. **Install dependencies**
   ```bash
   npm install
   ```

### Development Environment

**Option 1: Local Development with Docker Services**
```bash
# Start MongoDB and Kafka services
docker-compose -f docker-compose.dev.yml up mongodb kafka zookeeper -d

# Run the application locally
npm run dev
```

**Option 2: Full Docker Development**
```bash
# Start all services including the application
docker-compose -f docker-compose.dev.yml up -d
```

## 📋 Configuration

### Required Environment Variables

| Variable | Description | Example |
|----------|-------------|---------|
| `MONGO_INITDB_ROOT_USERNAME` | MongoDB username | `admin` |
| `MONGO_INITDB_ROOT_PASSWORD` | MongoDB password | `password123` |
| `MONGODB_HOST` | MongoDB host and port | `localhost:27017` |
| `ASHOST` | SAP application server host | `sap-server.company.com` |
| `SYSNR` | SAP system number | `00` |
| `CLIENT` | SAP client number | `300` |
| `SAPUSER` | SAP username | `sapuser` |
| `SAPPSW` | SAP password | `sappassword` |
| `KAFKA_BROKER` | Kafka broker addresses | `localhost:9092` |
| `KAFKA_TOPIC_VERSAPAY` | Versapay payment topic | `versapay_payment_dev` |
| `KAFKA_TOPIC_RECEIVED` | SAP GL received topic | `DEV.SAPGL.RECEIVED` |
| `VERSAPAY_ENDPOINT` | Versapay API endpoint | `https://api.versapay.com` |
| `VERSAPAY_USERNAME` | Versapay API username | `your_username` |
| `VERSAPAY_PASSWORD` | Versapay API password | `your_password` |

## 🔧 Production Deployment

### Non-Production Environment
```bash
docker-compose -f docker-compose.non-prod.yml up -d
```

### Production Environment
```bash
docker-compose -f docker-compose.prod.yml up -d
```

## 🧪 Testing

```bash
# Run unit tests
npm test

# Run with coverage
npm run test:coverage

# Run integration tests
npm run test:integration
```

## 📊 Monitoring & Logging

The application provides comprehensive logging for:
- Kafka message processing
- SAP RFC operations
- Versapay API interactions
- Database operations
- Error handling and retry mechanisms

## 🔍 Troubleshooting

### Common Issues

1. **SAP RFC Connection Issues**
   - Verify SAP server connectivity
   - Check RFC user permissions
   - Validate SAP system parameters

2. **Kafka Connection Problems**
   - Ensure Kafka brokers are accessible
   - Verify topic names and permissions
   - Check consumer group configuration

3. **Versapay API Errors**
   - Validate API credentials
   - Check API endpoint accessibility
   - Review rate limiting settings

## 📝 License

This project is licensed under the ISC License.

## 👥 Support

For support and questions, please contact the integration team or create an issue in the project repository.
