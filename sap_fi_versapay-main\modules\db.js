import mongoose from "mongoose";

const versapaySchema = new mongoose.Schema({
    _id: { _id: String },
    invoice_payments: { type: Object },
    sap_fi_docs: { type: Object },
    companies: { type: Object },
    invoices: { type: Object },
    t8a30: { type: Object },
    knb1: { type: Object },
    faglflexa: { type: Object }
}, {
    timestamps: true
});

const kafkaRetrySchema = new mongoose.Schema({
    _id: { _id: String },
    retry: { type: Number },
    errormessage: { type: String }
}, {
    timestamps: true
});



const versapayPayment = mongoose.model("versapayPayment", versapaySchema);
const kafkaRetry = mongoose.model("kafkaRetry", kafkaRetrySchema);

export { kafkaRetry, versapayPayment };