version: "3.9"

services:
  # MongoDB for local development
  mongodb:
    image: mongo:7.0
    container_name: sap_versapay_mongodb
    restart: unless-stopped
    ports:
      - "27017:27017"
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: password123
    volumes:
      - mongodb_data:/data/db
    networks:
      - sap_versapay_network

  # Zookeeper for Kafka
  zookeeper:
    image: confluentinc/cp-zookeeper:7.4.0
    container_name: sap_versapay_zookeeper
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000
    networks:
      - sap_versapay_network

  # Kafka for local development
  kafka:
    image: confluentinc/cp-kafka:7.4.0
    container_name: sap_versapay_kafka
    depends_on:
      - zookeeper
    ports:
      - "9092:9092"
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://localhost:9092
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
      KAFKA_AUTO_CREATE_TOPICS_ENABLE: true
    networks:
      - sap_versapay_network

  # SAP Versapay Integration Application
  sap_versapay_app:
    build: .
    container_name: sap_versapay_consumer
    restart: unless-stopped
    depends_on:
      - mongodb
      - kafka
    environment:
      - NODE_ENV=development
      - MONGO_INITDB_ROOT_USERNAME=admin
      - MONGO_INITDB_ROOT_PASSWORD=password123
      - MONGODB_HOST=mongodb:27017
      - KAFKA_BROKER=kafka:9092
      - KAFKA_CLIENTID=SAP.GL.POSTING
      - KAFKA_GROUPID=DEV-SAP-GL-POST-VERSAPAY
      - KAFKA_TOPIC_RECEIVED=DEV.SAPGL.RECEIVED
      - KAFKA_TOPIC_VERSAPAY=versapay_payment_dev
      - KAFKAJS_NO_PARTITIONER_WARNING=1
    env_file:
      - .env
    volumes:
      - .:/usr/src/app
      - /usr/src/app/node_modules
    networks:
      - sap_versapay_network

volumes:
  mongodb_data:

networks:
  sap_versapay_network:
    driver: bridge
