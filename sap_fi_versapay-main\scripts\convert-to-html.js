#!/usr/bin/env node
/**
 * Convert Versapay Mapping Documentation to HTML
 * This HTML can then be opened in Word and saved as .docx
 */

import fs from 'fs';
import path from 'path';

const inputFile = 'VERSAPAY_MAPPING_DOCUMENTATION.md';
const outputFile = 'Versapay_SAP_FI_Mapping_Documentation.html';

console.log('📄 Converting Versapay Mapping Documentation to HTML');

try {
    // Read the markdown file
    const markdownContent = fs.readFileSync(inputFile, 'utf8');
    
    // Basic markdown to HTML conversion
    let htmlContent = markdownContent
        // Headers
        .replace(/^# (.*$)/gm, '<h1>$1</h1>')
        .replace(/^## (.*$)/gm, '<h2>$1</h2>')
        .replace(/^### (.*$)/gm, '<h3>$1</h3>')
        .replace(/^#### (.*$)/gm, '<h4>$1</h4>')
        
        // Code blocks
        .replace(/```javascript\n([\s\S]*?)\n```/g, '<pre><code class="javascript">$1</code></pre>')
        .replace(/```\n([\s\S]*?)\n```/g, '<pre><code>$1</code></pre>')
        
        // Inline code
        .replace(/`([^`]+)`/g, '<code>$1</code>')
        
        // Bold text
        .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
        
        // Tables - basic conversion
        .replace(/\|(.+)\|/g, (match, content) => {
            const cells = content.split('|').map(cell => cell.trim());
            return '<tr>' + cells.map(cell => `<td>${cell}</td>`).join('') + '</tr>';
        })
        
        // Line breaks
        .replace(/\n\n/g, '</p><p>')
        .replace(/\n/g, '<br>');

    // Wrap in HTML structure
    const fullHtml = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Versapay to SAP FI Mapping Documentation</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            color: #333;
        }
        
        h1 {
            color: #2c3e50;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
            page-break-before: always;
        }
        
        h2 {
            color: #34495e;
            border-bottom: 2px solid #95a5a6;
            padding-bottom: 5px;
            margin-top: 30px;
        }
        
        h3 {
            color: #2c3e50;
            margin-top: 25px;
        }
        
        h4 {
            color: #7f8c8d;
            margin-top: 20px;
        }
        
        pre {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            overflow-x: auto;
            font-family: 'Courier New', monospace;
            font-size: 14px;
        }
        
        code {
            background-color: #f1f2f6;
            padding: 2px 4px;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
        }
        
        table {
            border-collapse: collapse;
            width: 100%;
            margin: 15px 0;
        }
        
        th, td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        
        th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        
        tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        
        .toc {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .toc ul {
            list-style-type: none;
            padding-left: 0;
        }
        
        .toc li {
            margin: 5px 0;
        }
        
        .toc a {
            text-decoration: none;
            color: #007bff;
        }
        
        .toc a:hover {
            text-decoration: underline;
        }
        
        .highlight {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
        }
        
        .note {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
        }
        
        @media print {
            body {
                font-size: 12pt;
            }
            
            h1 {
                page-break-before: always;
            }
            
            pre, table {
                page-break-inside: avoid;
            }
        }
    </style>
</head>
<body>
    <p>${htmlContent}</p>
</body>
</html>`;

    // Write HTML file
    fs.writeFileSync(outputFile, fullHtml);
    
    console.log('✅ HTML document created successfully!');
    console.log(`📄 Output file: ${outputFile}`);
    console.log('\n📋 To convert to Word:');
    console.log('1. Open the HTML file in a web browser');
    console.log('2. Print and select "Save as PDF" or');
    console.log('3. Copy content and paste into Microsoft Word');
    console.log('4. Save as .docx format');
    
    console.log('\n📊 Document contains:');
    console.log('   ✓ Complete field mapping documentation');
    console.log('   ✓ String token parsing methods');
    console.log('   ✓ SAP table query structures');
    console.log('   ✓ FI document generation logic');
    console.log('   ✓ Data conversion examples');
    console.log('   ✓ Professional styling for Word conversion');

} catch (error) {
    console.error('❌ Error creating HTML document:', error.message);
    console.log('\n📋 Manual steps:');
    console.log('1. Open VERSAPAY_MAPPING_DOCUMENTATION.md');
    console.log('2. Copy the content');
    console.log('3. Use an online markdown to Word converter');
    console.log('4. Or paste into Word and format manually');
}

console.log('\n🎉 Process completed!');
