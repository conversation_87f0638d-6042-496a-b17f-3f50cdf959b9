# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
dotenv

# Configuration files with sensitive data
aconfig/*.env
!aconfig/*.env.example

# SAP RFC logs and traces
dev_rfc.trc
dev_rfc.log
noderfc
_noderfc.log
*.saplog
*.trc

# Logs
logs/
*.log

# Coverage directory
coverage/
*.lcov

# Test artifacts
test-results/

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# Build artifacts
dist/
build/

# Temporary files
tmp/
temp/

# Docker
.dockerignore

# MongoDB
*.mongodb

# Kafka logs
kafka-logs/