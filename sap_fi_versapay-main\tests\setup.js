import { vi } from 'vitest';
import 'dotenv/config';

// Mock environment variables for testing
process.env.NODE_ENV = 'test';
process.env.MONGO_INITDB_ROOT_USERNAME = 'test_user';
process.env.MONGO_INITDB_ROOT_PASSWORD = 'test_password';
process.env.MONGODB_HOST = 'localhost:27017';
process.env.VERSAPAY_ENDPOINT = 'https://api-test.versapay.com';
process.env.VERSAPAY_USERNAME = 'test_user';
process.env.VERSAPAY_PASSWORD = 'test_password';
process.env.VERSAPAY_WHOAMI = 'test_element';
process.env.KAFKA_BROKER = 'localhost:9092';
process.env.KAFKA_TOPIC_VERSAPAY = 'test_versapay_topic';
process.env.KAFKA_TOPIC_RECEIVED = 'test_received_topic';

// Global test setup
beforeEach(() => {
    vi.clearAllMocks();
});

afterEach(() => {
    vi.restoreAllMocks();
});
