# Development Environment Configuration
# SAP FI Versapay Integration

# MongoDB Configuration
MONGO_INITDB_ROOT_USERNAME=admin
MONGO_INITDB_ROOT_PASSWORD=password123
MONGODB_HOST=mongodb:27017

# SAP RFC Configuration
ASHOST=your-sap-development-host.company.com
SYSNR=00
CLIENT=300
LANGU=EN
SAPUSER=your_sap_dev_user
SAPPSW=your_sap_dev_password
SYSTEM=DEV
RFC_TRACE=0

# Kafka Configuration
KAFKA_BROKER=kafka:9092
KAFKA_CLIENTID=SAP.GL.POSTING
KAFKA_GROUPID=DEV-SAP-GL-POST-VERSAPAY
KAFKA_TOPIC_RECEIVED=DEV.SAPGL.RECEIVED
KAFKA_TOPIC_VERSAPAY=versapay_payment_dev
KAFKAJS_NO_PARTITIONER_WARNING=1

# Versapay API Configuration
VERSAPAY_ENDPOINT=https://api-sandbox.versapay.com
VERSAPAY_USERNAME=your_versapay_dev_username
VERSAPAY_PASSWORD=your_versapay_dev_password
VERSAPAY_WHOAMI=your_whoami_element

# Application Configuration
NODE_ENV=development
LOG_LEVEL=debug
