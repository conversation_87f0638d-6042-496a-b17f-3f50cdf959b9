version: "3.9"

services:
  # Source (PIPO): <PERSON><PERSON><PERSON><PERSON>, Sentric
  # Versapay Mapping
  # Data Consumer and Posting to SAP-API
  consumer_versapay:
    container_name: consumer_versapay
    build: .
    image: consumer_versapay
    restart: always
    deploy:
      resources:
        limits:
          cpus: '0.50'
          memory: 1G
        reservations:
          cpus: '0.25'
          memory: 0.5G
    env_file:
      - "./aconfig/config.non-production.env"
    environment:
      - ASHOST=ud1-sv-sapeq1.matw.matthewsintl.com
      - SYSNR=00
      - CLIENT=300
      - LANGU=EN
      - SYSTEM=NQ1
      - RFC_TRACE=0
      - KAFKA_BROKER=dc6-sv-c3mft02.amer.schawk.com:9092,dc6-sv-c3mft02.amer.schawk.com:9093
      - KAFKA_CLIENTID=SAP.GL.POSTING
      - KAFKA_GROUPID=QA-SAP-GL-POST-VERSAPAY
      - KAFKA_TOPIC_RECEIVED=QA.SAPGL.RECEIVED
      - KAFKA_TOPIC_VERSAPAY=versapay_payment_qa
      - KAFKAJS_NO_PARTITIONER_WARNING=1
      - MONGODB_HOST=C3MFT01LD.amer.schawk.com:5311
      - VERSAPAY_ENDPOINT=https://uat.versapay.com
      - VERSAPAY_WHOAMI=zE-ecSsBGqz1HCsESEya