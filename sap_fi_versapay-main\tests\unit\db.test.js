import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import mongoose from 'mongoose';
import { versapayPayment, kafkaRetry } from '../../modules/db.js';

// Mock mongoose
vi.mock('mongoose');

describe('Database Models', () => {
    beforeEach(() => {
        vi.clearAllMocks();
    });

    afterEach(() => {
        vi.restoreAllMocks();
    });

    describe('versapayPayment Model', () => {
        it('should have correct schema structure', () => {
            expect(versapayPayment).toBeDefined();
            expect(versapayPayment.modelName).toBe('versapayPayment');
        });

        it('should validate required fields', async () => {
            const mockDoc = {
                _id: 'test_payment_ref',
                invoice_payments: { test: 'data' },
                sap_fi_docs: { test: 'data' },
                companies: { test: 'data' },
                invoices: { test: 'data' },
                t8a30: { test: 'data' },
                knb1: { test: 'data' },
                faglflexa: { test: 'data' }
            };

            // Mock the model methods
            const mockSave = vi.fn().mockResolvedValue(mockDoc);
            const mockCreate = vi.fn().mockResolvedValue(mockDoc);
            
            versapayPayment.prototype.save = mockSave;
            versapayPayment.create = mockCreate;

            const result = await versapayPayment.create(mockDoc);
            expect(result).toEqual(mockDoc);
        });
    });

    describe('kafkaRetry Model', () => {
        it('should have correct schema structure', () => {
            expect(kafkaRetry).toBeDefined();
            expect(kafkaRetry.modelName).toBe('kafkaRetry');
        });

        it('should handle retry logic', async () => {
            const mockRetryDoc = {
                _id: 'test_retry_id',
                retry: 3,
                errormessage: 'Test error message'
            };

            const mockSave = vi.fn().mockResolvedValue(mockRetryDoc);
            const mockCreate = vi.fn().mockResolvedValue(mockRetryDoc);
            
            kafkaRetry.prototype.save = mockSave;
            kafkaRetry.create = mockCreate;

            const result = await kafkaRetry.create(mockRetryDoc);
            expect(result).toEqual(mockRetryDoc);
        });
    });

    describe('Database Connection', () => {
        it('should handle connection errors gracefully', () => {
            const mockConnect = vi.fn().mockRejectedValue(new Error('Connection failed'));
            mongoose.connect = mockConnect;

            expect(() => {
                mongoose.connect('**********************************');
            }).not.toThrow();
        });
    });
});
