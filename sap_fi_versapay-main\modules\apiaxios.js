import "dotenv/config";
import axios from "axios";

const {
    VERSAPAY_ENDPOINT: versapayURL,
    VERSAPAY_WHOAMI: whoami_element,
    VERSAPAY_USERNAME: versapay_username,
    VERSAPAY_PASSWORD: versapay_password
} = process.env;

// Set config defaults when creating the instance
const insaxios = axios.create({
    baseURL: versapayURL,
    timeout: 3000
});

const reqCofig = {
    auth: {
        username: versapay_username,
        password: versapay_password
    }
}

const versapayConnect = () => {
    return new Promise(async (resolve, reject) => {
        try {
            // Get Versapay Auth Token
            const resp = await insaxios.get('/api/whoami', reqCofig);
            resolve(resp);
        } catch (error) {
            reject(new Error(error));
        }
    })
}

const getChildrensPayments = (reference_or_token) => {
    return new Promise(async (resolve, reject) => {
        try {
            // Get Versapay Auth Token
            const { status: jwtstatus, data } = await insaxios.get('/api/whoami', reqCofig);
            if (jwtstatus === 200) {
                const { jwt } = data.whoami[`${whoami_element}`];
                const rurl = `/api/exports/payment/${reference_or_token}`;

                const rconfig = {
                    params: { 'options[export_payment_tree]': true },
                    headers: { Authorization: `Bearer ${jwt}` }
                }
                // Get Versapay Children payment_reference
                const root = await insaxios.get(rurl, rconfig);
                const { status: rstatus, data: { payment_amounts: payments, payment_tree: { children } } } = root;
                if (rstatus === 200 && children?.length > 0) {
                    const curls = children.map(ref => `/api/exports/payment/${ref}`);
                    const headers = { Authorization: `Bearer ${jwt}` };
                    // const childrens = await Promise.all(curls.map((url) => insaxios.get(url, { headers, timeout: 1000 })));
                    try {
                        let childrens = await Promise.all(curls.map((url) => insaxios.get(url, { headers, timeout: 3000 })));
                        childrens.forEach(({ data: { payment_method, payment_amounts } }) => {
                            // Exclude Discounted Childrens


                            if (!['Discount'].includes(payment_method)) {
                                payment_amounts.forEach(credit => { // Each Children Invoice Amount
                                    const i = payments.findIndex(payment => (payment.invoice_number === credit.invoice_number));
                                    if (i >= 0) { // Do sum of Same invoice in different Children Credit Payments
                                        payments[i].amount = Number.parseFloat((
                                            Number(payments[i].amount) + Number(credit.amount)
                                        )).toFixed(2);
                                    } else {
                                        payments.push(credit)
                                    }
                                })
                            }
                        });
                    } catch (axioserr) {
                        throw new Error(axioserr.message);
                    }
                }
                resolve(payments);
            } else {
                throw new Error(`Versapay API Status: ${jwtstatus}`);
            }
        } catch (error) {
            reject(new Error(error.message));
        }
    })
}

export { versapayConnect, getChildrensPayments };