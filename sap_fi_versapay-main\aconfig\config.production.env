# Production Environment Configuration
# SAP FI Versapay Integration

# MongoDB Configuration
MONGO_INITDB_ROOT_USERNAME=sapversapay_prod_user
MONGO_INITDB_ROOT_PASSWORD=your_very_secure_mongo_password
MONGODB_HOST=your-prod-mongo-host:27017

# SAP RFC Configuration
ASHOST=ud3-sv-sapec3.matw.matthewsintl.com
SYSNR=00
CLIENT=300
LANGU=EN
SAPUSER=your_sap_prod_user
SAPPSW=your_sap_prod_password
SYSTEM=NP1
RFC_TRACE=0

# Kafka Configuration
KAFKA_BROKER=dc6-sv-c3mft02.amer.schawk.com:9092,dc6-sv-c3mft02.amer.schawk.com:9093
KAFKA_CLIENTID=SAP.GL.POSTING
KAFKA_GROUPID=SAP-GL-POST-VERSAPAY
KAFKA_TOPIC_RECEIVED=GL.SAPGL.RECEIVED
KAFKA_TOPIC_VERSAPAY=versapay_payment_prd
KAFKAJS_NO_PARTITIONER_WARNING=1

# Versapay API Configuration
VERSAPAY_ENDPOINT=https://api.versapay.com
VERSAPAY_USERNAME=your_versapay_prod_username
VERSAPAY_PASSWORD=your_versapay_prod_password
VERSAPAY_WHOAMI=your_whoami_element

# Application Configuration
NODE_ENV=production
LOG_LEVEL=warn
