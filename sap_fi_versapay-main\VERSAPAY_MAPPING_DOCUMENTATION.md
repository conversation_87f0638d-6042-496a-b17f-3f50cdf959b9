# Versapay to SAP FI Mapping Documentation

## Document Overview
**Document Title:** Versapay Payment Processing and SAP FI Document Generation  
**Module:** versapayMapping.js  
**Purpose:** Field mapping, data conversion, and SAP FI document creation from Versapay payment data  
**Date:** 2025-06-27  

---

## Table of Contents
1. [Class Structure](#class-structure)
2. [Main Processing Method](#main-processing-method)
3. [Field Mappings and Conversions](#field-mappings-and-conversions)
4. [SAP Table Queries](#sap-table-queries)
5. [SAP FI Document Generation](#sap-fi-document-generation)
6. [String Token Parsing Methods](#string-token-parsing-methods)
7. [Data Flow Diagram](#data-flow-diagram)

---

## Class Structure

### sapClient Class
```javascript
export default class sapClient {
    #abapClient  // Private SAP RFC client instance
    
    constructor(abap) {
        this.#abapClient = new rfcClient(abap);
    }
}
```

**Purpose:** Main class for handling Versapay payment processing and SAP FI document generation.

---

## Main Processing Method

### versapayPayentPosting()
**Input Parameters:**
- `payment_reference` (String): Unique payment identifier from Versapay
- `customer_identifier` (String): Customer ID in SAP system
- `payment_transaction_fee` (Number): Transaction fee amount
- `payment_amounts` (Array): Array of payment line items

**Processing Flow:**
1. Calculate invoice payments and extract company information
2. Query SAP tables for GL accounts and customer data
3. Generate SAP FI documents
4. Store processing data in MongoDB
5. Return SAP FI documents for Kafka publishing

**Output:** Array of SAP FI documents ready for posting

---

## Field Mappings and Conversions

### Input Data Structure (Versapay Payment)
```javascript
payment_amounts.forEach(payment => {
    let {
        payment_reference,        // String: Versapay payment reference
        customer_identifier,      // String: Customer ID
        date,                    // String: Payment date (YYYY-MM-DD)
        invoice_number,          // String: Invoice number
        amount,                  // String/Number: Payment amount
        payment_method,          // String: Payment method
        short_pay_reason,        // String: Reason for short payment
        invoice_external_id,     // String/Number: Discount amount
        invoice_amount,          // String/Number: Total invoice amount
        invoice_currency,        // String: Currency code
        invoice_internal_number, // String: Internal invoice reference
        payment_note            // String: Payment notes
    } = payment;
});
```

### String Token Parsing Methods

#### 1. Company Code Extraction
```javascript
// Extract Company Code (First 4 characters)
let startPos = 0;
let endPos = 4;
const compCode = invoice_internal_number.slice(startPos, endPos);
```
**Input:** `invoice_internal_number` (e.g., "1000INV001202400001")  
**Output:** `compCode` (e.g., "1000")  
**Purpose:** Extract SAP company code for document posting

#### 2. Fiscal Year Extraction
```javascript
// Extract Fiscal Year (4 characters after invoice number)
startPos = (endPos + invoice_number.length);
endPos = (startPos + 4);
const fiscalYear = invoice_internal_number.slice(startPos, endPos);
```
**Input:** `invoice_internal_number` + `invoice_number.length`  
**Output:** `fiscalYear` (e.g., "2024")  
**Purpose:** Determine fiscal year for SAP document

#### 3. Line Item Number Extraction
```javascript
// Extract Item Number (Next 3 characters)
startPos = endPos;
endPos = (startPos + 3);
const itemNum = invoice_internal_number.slice(startPos, endPos);
```
**Input:** Continuation of `invoice_internal_number`  
**Output:** `itemNum` (e.g., "001")  
**Purpose:** SAP document line item reference

### Data Type Conversions

#### Numeric Conversions
```javascript
// Convert string amounts to numbers
amount = Number(amount);                           // Customer paid amount
invoice_amount = Number(invoice_amount);           // Total invoice amount
invoice_external_id = Number(invoice_external_id); // Discount amount
```

#### Amount Calculations
```javascript
// SAP invoice amount (after discount)
const sap_invoice_amount = Number.parseFloat(invoice_amount - invoice_external_id).toFixed(2);

// Balance calculation (overpay/underpay)
const balance_amount = Number.parseFloat(amount - sap_invoice_amount).toFixed(2);
```

#### String Formatting
```javascript
// Invoice number padding
invoice_number: invoice_number.padStart(10, '0')  // Pad to 10 digits

// Currency normalization
invoice_currency: invoice_currency.toUpperCase()  // Convert to uppercase

// Item text concatenation
const itemText = `${invoice_number} ${short_pay_reason || ""} ${payment_note || ""}`;
```

### Output Data Structure (Processed Payment)
```javascript
invoice_payments.push({
    payment_reference,                                    // Original reference
    payment_method,                                       // Payment method
    customer_identifier,                                  // Customer ID
    date,                                                // Payment date
    compCode,                                            // Extracted company code
    invoice_number: invoice_number.padStart(10, '0'),    // Padded invoice number
    fiscalYear,                                          // Extracted fiscal year
    itemNum,                                             // Extracted item number
    payment_transaction_fee: "0.00",                     // Fee (set to first item)
    amount: Number.parseFloat(amount).toFixed(2),        // Formatted amount
    invoice_amount: Number.parseFloat(invoice_amount).toFixed(2),
    invoice_external_id: Number.parseFloat(invoice_external_id).toFixed(2),
    sap_invoice_amount: Number.parseFloat(sap_invoice_amount).toFixed(2),
    balance_amount: Number.parseFloat(balance_amount).toFixed(2),
    invoice_currency: invoice_currency.toUpperCase(),     // Uppercase currency
    itemText                                             // Concatenated text
});
```

---

## SAP Table Queries

### 1. FAGL_T8A30 - General Ledger Default Profit Center
**Purpose:** Get default profit center for GL account **********

**Query Construction:**
```javascript
// Build dynamic WHERE clause for multiple companies
Object.keys(companies).forEach((comp, i) => {
    if ((i + 1) === 1) {
        options_t8a30.push({ TEXT: `( ` });
        options_t8a30.push({ TEXT: `BUKRS = '${comp}' ` });
    } else {
        options_t8a30.push({ TEXT: `OR BUKRS = '${comp}' ` });
    }
    if ((i + 1) === Object.keys(companies).length) {
        options_t8a30.push({ TEXT: `) ` });
    }
});
options_t8a30.push({ TEXT: `AND KONTO_VON = '${gl100012}'` });
```

**Response Parsing:**
```javascript
// Parse pipe-delimited response: "310|1000|**********|**********|0010|**********|1000"
const column = e.WA.split("|");
t8a30_final.push({
    BUKRS: column[1],      // Company code
    KONTO_VON: column[2],  // GL account from
    PRCTR: column[5],      // Profit center
})
```

### 2. KNB1 - Customer Master (Company Code)
**Purpose:** Get customer reconciliation account in General Ledger

**Query Construction:**
```javascript
// Customer query with company codes
let options_knb1 = [{ TEXT: `KUNNR = '${customer_identifier.padStart(10, '0')}' AND ` }];
// ... dynamic company code conditions
options_knb1.push({ TEXT: `AND LOEVM = ''` }); // Not marked for deletion
```

**Field Selection:**
```javascript
FIELDS: [
    { FIELDNAME: "KUNNR", OFFSET: "000000", LENGTH: "000010" }, // Customer number
    { FIELDNAME: "BUKRS", OFFSET: "000011", LENGTH: "000004" }, // Company code
    { FIELDNAME: "AKONT", OFFSET: "000016", LENGTH: "000010" }  // Reconciliation account
]
```

**Response Parsing:**
```javascript
// Parse response: "**********|1000|**********"
const column = e.WA.split("|");
knb1_final.push({
    KUNNR: column[0],  // Customer number
    BUKRS: column[1],  // Company code
    AKONT: column[2],  // Reconciliation account
})
```

### 3. FAGLFLEXA - General Ledger Line Items
**Purpose:** Get profit center information from existing invoice documents

**Query Construction:**
```javascript
// Build query for multiple invoices
invoices.forEach((inv, i) => {
    const { BUKRS, BELNR, GJAHR } = inv;
    if ((i + 1) === 1) {
        options_faglflexa.push({
            TEXT: `( RYEAR = '${GJAHR}' AND DOCNR = '${BELNR}' `
        })
    } else {
        options_faglflexa.push({
            TEXT: `OR ( RYEAR = '${GJAHR}' AND DOCNR = '${BELNR}' `
        })
    }
    const AKONT = knb1.find(f => f.BUKRS === BUKRS).AKONT;
    if (AKONT) {
        options_faglflexa.push({
            TEXT: `AND RLDNR = '0L' AND RBUKRS = '${BUKRS}' AND RACCT = '${AKONT}' ) `
        });
    }
});
```

**Field Selection:**
```javascript
FIELDS: [
    { FIELDNAME: "RYEAR", OFFSET: "000000", LENGTH: "000004" },  // Fiscal year
    { FIELDNAME: "DOCNR", OFFSET: "000005", LENGTH: "000010" },  // Document number
    { FIELDNAME: "RLDNR", OFFSET: "000016", LENGTH: "000002" },  // Ledger
    { FIELDNAME: "RBUKRS", OFFSET: "000019", LENGTH: "000004" }, // Company code
    { FIELDNAME: "DOCLN", OFFSET: "000024", LENGTH: "000006" },  // Line item
    { FIELDNAME: "RACCT", OFFSET: "000031", LENGTH: "000010" },  // Account
    { FIELDNAME: "PRCTR", OFFSET: "000042", LENGTH: "000010" }   // Profit center
]
```

**Response Parsing:**
```javascript
// Parse response: "2019|**********|0L|1000|000001|**********|**********"
const column = e.WA.split("|");
faglflexa_final.push({
    RYEAR: column[0],   // Fiscal year
    DOCNR: column[1],   // Document number
    RBUKRS: column[3],  // Company code
    RACCT: column[5],   // Account number
    PRCTR: column[6]    // Profit center
})
```

---

## SAP FI Document Generation

### Document Header Structure
```javascript
Header: {
    COMP_CODE: compCode,                                    // Company code
    DOC_TYPE: (balance_amount === 0.00) ? "DZ" : "AB",    // Document type
    DOC_DATE: (new Date()).toISOString().slice(0, 10).replace(/-/g, ""), // Current date
    PSTNG_DATE: date.replaceAll("-", ""),                  // Posting date
    HEADER_TXT: "VERSAPAY",                                // Header text
    REF_DOC_NO: payment_reference,                         // Reference number
    BUS_ACT: "RFBU"                                        // Business activity
}
```

### Document Type Logic
- **"DZ"** - Used when `balance_amount === 0.00` (Exact payment)
- **"AB"** - Used for overpayments or underpayments

### Line Item Generation

#### Item 1: Cash/Bank Account (Always Present)
```javascript
{
    Item_Number: "1",
    General_Ledger: {
        GL_ACCOUNT: it8a30.KONTO_VON,    // GL account (**********)
        COMP_CODE: it8a30.BUKRS,         // Company code
        PROFIT_CTR: it8a30.PRCTR,        // Profit center from T8A30
        ITEM_TEXT: itemText              // Concatenated text
    },
    Currency: {
        CURRENCY: invoice_currency,       // Currency code
        CURRENCY_ISO: invoice_currency,   // ISO currency
        AMT_DOCCUR: (amount + payment_transaction_fee).toFixed(2) // Total amount
    }
}
```

#### Item 2: Overpayment/Transaction Fee (Conditional)
**Condition:** `(balance_amount > 0) || (payment_transaction_fee > 0)`
```javascript
{
    Item_Number: "2",
    General_Ledger: {
        GL_ACCOUNT: gl440020,             // GL account (**********)
        COMP_CODE: compCode,              // Company code
        PROFIT_CTR: cfaglflexa?.PRCTR ?? '', // Profit center from FAGLFLEXA
        ITEM_TEXT: itemText               // Item text
    },
    Currency: {
        CURRENCY: invoice_currency,
        CURRENCY_ISO: invoice_currency,
        AMT_DOCCUR: ((balance_amount > 0 ? balance_amount : 0) + payment_transaction_fee) * (-1) // Negative amount
    }
}
```

#### Item 3: Underpayment (Conditional)
**Condition:** `balance_amount < 0`
```javascript
{
    Item_Number: "3",
    Customer: {
        CUSTOMER: customer_identifier.padStart(10, '0'), // Padded customer number
        COMP_CODE: compCode,                             // Company code
        PROFIT_CTR: cfaglflexa?.PRCTR ?? '',            // Profit center
        ITEM_TEXT: itemText                             // Item text
    },
    Currency: {
        CURRENCY: invoice_currency,
        CURRENCY_ISO: invoice_currency,
        AMT_DOCCUR: (balance_amount * (-1)).toFixed(2)  // Positive amount (absolute value)
    }
}
```

#### Final Item: Customer Clearing (Always Present)
```javascript
{
    Item_Number: item_num.toString(),
    Customer: {
        CUSTOMER: customer_identifier.padStart(10, '0'), // Customer number
        COMP_CODE: compCode,                             // Company code
        PROFIT_CTR: cfaglflexa?.PRCTR ?? '',            // Profit center
        ITEM_TEXT: itemText                             // Item text
    },
    Currency: {
        CURRENCY: invoice_currency,
        CURRENCY_ISO: invoice_currency,
        AMT_DOCCUR: (sap_invoice_amount * (-1)).toFixed(2) // Negative invoice amount
    },
    CustomerClearing: [{
        BELNR: invoice_number,  // Invoice document number
        BUKRS: compCode,        // Company code
        GJAHR: fiscalYear,      // Fiscal year
        BUZEI: itemNum          // Line item number
    }]
}
```

---

## String Token Parsing Methods Summary

### Method 1: invoice_internal_number Parsing
**Format:** `[CompanyCode][InvoiceNumber][FiscalYear][ItemNumber]`
**Example:** `"1000INV001202400001"`

| Position | Length | Field | Example | Purpose |
|----------|--------|-------|---------|---------|
| 0-3 | 4 | Company Code | "1000" | SAP company identification |
| 4-[invoice_number.length] | Variable | Invoice Number | "INV001" | Invoice identifier |
| [4+inv_len]-[4+inv_len+3] | 4 | Fiscal Year | "2024" | Document fiscal year |
| [4+inv_len+4]-[4+inv_len+6] | 3 | Item Number | "001" | Line item reference |

### Method 2: SAP Table Response Parsing
**Format:** Pipe-delimited strings from RFC_READ_TABLE

#### T8A30 Response Format:
`"310|1000|**********|**********|0010|**********|1000"`
- Position 1: Company Code (BUKRS)
- Position 2: GL Account From (KONTO_VON)
- Position 5: Profit Center (PRCTR)

#### KNB1 Response Format:
`"**********|1000|**********"`
- Position 0: Customer Number (KUNNR)
- Position 1: Company Code (BUKRS)
- Position 2: Reconciliation Account (AKONT)

#### FAGLFLEXA Response Format:
`"2019|**********|0L|1000|000001|**********|**********"`
- Position 0: Fiscal Year (RYEAR)
- Position 1: Document Number (DOCNR)
- Position 3: Company Code (RBUKRS)
- Position 5: Account Number (RACCT)
- Position 6: Profit Center (PRCTR)

---

## Data Flow Diagram

```
Versapay Payment Data
         ↓
┌─────────────────────────┐
│ invoicePaymentCalculation│
│ - Parse invoice_internal_number │
│ - Extract company, fiscal year  │
│ - Calculate amounts            │
│ - Format strings              │
└─────────────────────────┘
         ↓
┌─────────────────────────┐
│ SAP Table Queries      │
│ - get_fagl_t8a30       │
│ - get_knb1             │
│ - get_faglflexa        │
└─────────────────────────┘
         ↓
┌─────────────────────────┐
│ CreateSapFiDoc         │
│ - Generate header      │
│ - Create line items    │
│ - Handle overpay/underpay │
│ - Add customer clearing │
└─────────────────────────┘
         ↓
    SAP FI Document
```

---

## Key Constants and GL Accounts

| Constant | Value | Purpose |
|----------|-------|---------|
| gl100012 | "**********" | Cash/Bank GL Account |
| gl440020 | "**********" | Overpayment/Fee GL Account |
| DOC_TYPE_DZ | "DZ" | Exact payment document type |
| DOC_TYPE_AB | "AB" | Over/Under payment document type |
| BUS_ACT | "RFBU" | Business activity code |
| HEADER_TXT | "VERSAPAY" | Document header text |

---

## Processing Logic Summary

### Payment Scenarios

#### 1. Exact Payment (balance_amount = 0)
- **Document Type:** DZ
- **Line Items:** 2 items
  1. Cash/Bank account (Debit)
  2. Customer clearing (Credit)

#### 2. Overpayment (balance_amount > 0)
- **Document Type:** AB
- **Line Items:** 3 items
  1. Cash/Bank account (Debit - full amount)
  2. Overpayment GL account (Credit - overpayment)
  3. Customer clearing (Credit - invoice amount)

#### 3. Underpayment (balance_amount < 0)
- **Document Type:** AB
- **Line Items:** 3 items
  1. Cash/Bank account (Debit - payment amount)
  2. Customer account (Debit - underpayment)
  3. Customer clearing (Credit - invoice amount)

#### 4. Transaction Fee Processing
- **Applied to:** First invoice payment only
- **Handling:** Added to cash/bank account debit
- **GL Account:** Posted to overpayment account (gl440020)

---

**Document End**
```
```
