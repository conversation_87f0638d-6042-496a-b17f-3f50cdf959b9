# Build Instructions for SAP FI Versapay Integration

## 🚨 Important: SAP RFC Dependency Issue

The `node-rfc` package (v3.3.1) is **deprecated** and requires specific build tools and Python for compilation on Windows. This document provides multiple solutions to build and run the application.

## 🐳 Solution 1: Docker Development (Recommended)

The application uses a custom Docker base image that includes pre-compiled SAP RFC libraries. This is the most reliable approach.

### Prerequisites
- Docker Desktop for Windows
- Docker Compose

### Steps
1. **Build and run with Docker Compose:**
   ```bash
   # For development environment
   docker-compose -f docker-compose.dev.yml up --build

   # For non-production environment
   docker-compose -f docker-compose.non-prod.yml up --build

   # For production environment
   docker-compose -f docker-compose.prod.yml up --build
   ```

2. **Access application logs:**
   ```bash
   docker-compose -f docker-compose.dev.yml logs -f sap_versapay_app
   ```

## 🔧 Solution 2: Windows Build Environment Setup

If you need to run locally on Windows, you'll need to set up the build environment:

### Prerequisites
1. **Install Python 3.8-3.11:**
   ```bash
   # Download from https://www.python.org/downloads/
   # Or use Windows Package Manager
   winget install Python.Python.3.11
   ```

2. **Install Visual Studio Build Tools:**
   ```bash
   # Download Visual Studio Build Tools 2022
   # Or install via winget
   winget install Microsoft.VisualStudio.2022.BuildTools
   ```

3. **Install Windows SDK:**
   ```bash
   # Install Windows 10/11 SDK
   winget install Microsoft.WindowsSDK.10
   ```

4. **Configure npm to use Python:**
   ```bash
   npm config set python "C:\Python311\python.exe"
   npm config set msvs_version 2022
   ```

### Build Steps
```bash
# Clean install
rm -rf node_modules package-lock.json
npm install

# If still failing, try with specific Python path
npm install --python="C:\Python311\python.exe"
```

## 🔄 Solution 3: Alternative SAP Connectivity

Consider migrating to modern SAP connectivity options:

### Option A: SAP REST APIs
Replace RFC calls with SAP OData/REST services:
```javascript
// Instead of RFC calls, use HTTP requests to SAP Gateway
import axios from 'axios';

const sapRestClient = axios.create({
    baseURL: 'https://your-sap-gateway.com/sap/opu/odata/sap/',
    auth: {
        username: process.env.SAP_USERNAME,
        password: process.env.SAP_PASSWORD
    }
});
```

### Option B: SAP Cloud Connector
Use SAP Cloud Platform connectivity services.

### Option C: SAP Business Technology Platform (BTP)
Leverage SAP BTP integration services.

## 🐛 Troubleshooting

### Common Issues

1. **Python Not Found:**
   ```bash
   # Set Python path explicitly
   npm config set python "C:\Path\To\python.exe"
   ```

2. **Visual Studio Build Tools Missing:**
   ```bash
   # Install build tools
   npm install --global windows-build-tools
   ```

3. **SAP RFC Library Missing:**
   - Ensure SAP NetWeaver RFC SDK is installed
   - Set environment variables for RFC libraries

4. **Docker Build Fails:**
   ```bash
   # Clean Docker cache
   docker system prune -a
   docker-compose down --volumes
   docker-compose up --build
   ```

## 📋 Environment Variables for Docker

Create `aconfig/config.development.env`:
```env
# MongoDB
MONGO_INITDB_ROOT_USERNAME=admin
MONGO_INITDB_ROOT_PASSWORD=password123
MONGODB_HOST=mongodb:27017

# SAP RFC
ASHOST=your-sap-host
SYSNR=00
CLIENT=300
LANGU=EN
SAPUSER=your-sap-user
SAPPSW=your-sap-password
SYSTEM=DEV

# Kafka
KAFKA_BROKER=kafka:9092
KAFKA_CLIENTID=SAP.GL.POSTING
KAFKA_GROUPID=DEV-SAP-GL-POST-VERSAPAY
KAFKA_TOPIC_RECEIVED=DEV.SAPGL.RECEIVED
KAFKA_TOPIC_VERSAPAY=versapay_payment_dev

# Versapay
VERSAPAY_ENDPOINT=https://api.versapay.com
VERSAPAY_USERNAME=your-versapay-user
VERSAPAY_PASSWORD=your-versapay-password
VERSAPAY_WHOAMI=your-whoami-element
```

## 🚀 Quick Start (Docker)

```bash
# 1. Clone and navigate
git clone <repository-url>
cd sap_fi_versapay

# 2. Create environment config
mkdir -p aconfig
cp .env.example aconfig/config.development.env
# Edit the config file with your actual values

# 3. Build and run
docker-compose -f docker-compose.dev.yml up --build

# 4. Monitor logs
docker-compose -f docker-compose.dev.yml logs -f
```

## 📞 Support

If you encounter issues:
1. Check Docker logs: `docker-compose logs`
2. Verify environment variables
3. Ensure SAP system connectivity
4. Contact the integration team for SAP RFC library access
