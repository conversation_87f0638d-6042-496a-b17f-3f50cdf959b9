# SAP FI Versapay Integration - Deployment Guide

## 🎯 Overview

This guide provides step-by-step instructions for deploying the SAP FI Versapay Integration application across different environments.

## 📋 Prerequisites

### System Requirements
- **Docker Desktop** (Windows/Mac) or Docker Engine (Linux)
- **Docker Compose** v2.0+
- **Git** for version control
- **PowerShell** (Windows) or **Bash** (Linux/Mac)

### Access Requirements
- SAP system access with RFC connectivity
- Versapay API credentials
- Kafka cluster access
- MongoDB instance or cluster

## 🚀 Quick Deployment

### Step 1: Clone and Setup
```bash
# Clone the repository
git clone <repository-url>
cd sap_fi_versapay

# Make build scripts executable (Linux/Mac)
chmod +x scripts/build.sh
```

### Step 2: Configure Environment
```bash
# Copy and edit configuration files
cp .env.example .env

# Create environment-specific configs
mkdir -p aconfig
cp .env.example aconfig/config.development.env
cp .env.example aconfig/config.non-production.env
cp .env.example aconfig/config.production.env

# Edit each config file with actual values
```

### Step 3: Deploy

**Windows (PowerShell):**
```powershell
# Development environment
.\scripts\build.ps1 dev

# Staging environment
.\scripts\build.ps1 staging

# Production environment
.\scripts\build.ps1 prod
```

**Linux/Mac (Bash):**
```bash
# Development environment
./scripts/build.sh dev

# Staging environment
./scripts/build.sh staging

# Production environment
./scripts/build.sh prod
```

## 🔧 Manual Deployment

### Development Environment
```bash
# Start infrastructure services
docker-compose -f docker-compose.dev.yml up mongodb kafka zookeeper -d

# Wait for services to be ready (30-60 seconds)
docker-compose -f docker-compose.dev.yml logs kafka

# Start the application
docker-compose -f docker-compose.dev.yml up sap_versapay_app --build
```

### Production Environment
```bash
# Deploy to production
docker-compose -f docker-compose.prod.yml up --build -d

# Monitor deployment
docker-compose -f docker-compose.prod.yml logs -f
```

## 📊 Monitoring and Health Checks

### Container Status
```bash
# Check all containers
docker-compose -f docker-compose.prod.yml ps

# View application logs
docker-compose -f docker-compose.prod.yml logs -f consumer_versapay

# Check resource usage
docker stats
```

### Application Health
```bash
# Check MongoDB connection
docker-compose -f docker-compose.prod.yml exec consumer_versapay node -e "
const mongoose = require('mongoose');
mongoose.connect(process.env.MONGODB_URL).then(() => console.log('DB OK')).catch(console.error);
"

# Check Kafka connectivity
docker-compose -f docker-compose.prod.yml logs kafka | grep "started"
```

## 🔍 Troubleshooting

### Common Issues

1. **Docker Build Fails**
   ```bash
   # Clean Docker cache
   docker system prune -a
   docker-compose down --volumes
   ```

2. **SAP RFC Connection Issues**
   - Verify SAP server accessibility
   - Check RFC user permissions
   - Validate environment variables

3. **Kafka Connection Problems**
   - Ensure Kafka brokers are running
   - Check topic creation
   - Verify consumer group permissions

4. **MongoDB Connection Issues**
   - Check MongoDB service status
   - Verify connection string
   - Ensure database permissions

### Debug Commands
```bash
# Enter container for debugging
docker-compose -f docker-compose.prod.yml exec consumer_versapay bash

# Check environment variables
docker-compose -f docker-compose.prod.yml exec consumer_versapay env

# Test network connectivity
docker-compose -f docker-compose.prod.yml exec consumer_versapay ping kafka
docker-compose -f docker-compose.prod.yml exec consumer_versapay ping mongodb
```

## 🔄 Updates and Maintenance

### Application Updates
```bash
# Pull latest code
git pull origin main

# Rebuild and deploy
docker-compose -f docker-compose.prod.yml up --build -d

# Check deployment status
docker-compose -f docker-compose.prod.yml ps
```

### Database Maintenance
```bash
# Backup MongoDB
docker-compose -f docker-compose.prod.yml exec mongodb mongodump --out /backup

# View database collections
docker-compose -f docker-compose.prod.yml exec mongodb mongo --eval "show dbs"
```

### Log Management
```bash
# Rotate logs
docker-compose -f docker-compose.prod.yml logs --tail=1000 > app.log

# Clear old logs
docker system prune --volumes
```

## 🛡️ Security Considerations

1. **Environment Variables**
   - Never commit `.env` files to version control
   - Use secrets management in production
   - Rotate credentials regularly

2. **Network Security**
   - Use private networks for internal communication
   - Implement firewall rules
   - Enable TLS for external connections

3. **Access Control**
   - Limit container privileges
   - Use non-root users in containers
   - Implement proper RBAC

## 📈 Scaling

### Horizontal Scaling
```bash
# Scale application instances
docker-compose -f docker-compose.prod.yml up --scale consumer_versapay=3 -d
```

### Resource Limits
Edit docker-compose files to adjust resource limits:
```yaml
deploy:
  resources:
    limits:
      cpus: '1.0'
      memory: 2G
    reservations:
      cpus: '0.5'
      memory: 1G
```

## 📞 Support

For deployment issues:
1. Check application logs
2. Verify configuration files
3. Test connectivity to external services
4. Contact the development team with error details
