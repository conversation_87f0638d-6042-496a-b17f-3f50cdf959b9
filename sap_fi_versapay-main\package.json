{"name": "sap_fi_versapay", "version": "1.0.0", "description": "", "type": "module", "main": "index.js", "scripts": {"start": "node index.js", "dev": "nodemon index.js", "test": "vitest", "test:coverage": "vitest --coverage", "test:integration": "vitest --config vitest.integration.config.js", "test:watch": "vitest --watch", "test:sap": "node test-sap-connection.js", "build": "echo 'Build completed - Node.js application'", "docker:build": "docker build -t sap-versapay-integration .", "docker:dev": "docker-compose -f docker-compose.dev.yml up --build", "docker:prod": "docker-compose -f docker-compose.prod.yml up --build", "lint": "eslint .", "lint:fix": "eslint . --fix"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"axios": "^1.7.7", "kafkajs": "^2.2.4", "mongoose": "^8.7.3", "node-rfc": "^3.3.1"}, "devDependencies": {"dotenv": "^16.4.5", "nodemon": "^3.1.7", "vitest": "^2.1.8", "@vitest/coverage-v8": "^2.1.8", "eslint": "^9.17.0", "@eslint/js": "^9.17.0"}}