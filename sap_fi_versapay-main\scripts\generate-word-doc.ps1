# PowerShell Script to Generate Word Document from Markdown
# This script converts the Versapay mapping documentation to Word format

param(
    [Parameter(Mandatory=$false)]
    [string]$InputFile = "VERSAPAY_MAPPING_DOCUMENTATION.md",
    [Parameter(Mandatory=$false)]
    [string]$OutputFile = "Versapay_SAP_FI_Mapping_Documentation.docx"
)

Write-Host "📄 Converting Versapay Mapping Documentation to Word Format" -ForegroundColor Green

try {
    # Check if input file exists
    if (-not (Test-Path $InputFile)) {
        throw "Input file '$InputFile' not found."
    }

    # Check if Microsoft Word is available
    try {
        $Word = New-Object -ComObject Word.Application
        $Word.Visible = $false
        Write-Host "✅ Microsoft Word found" -ForegroundColor Green
    } catch {
        Write-Host "❌ Microsoft Word not found. Trying alternative method..." -ForegroundColor Yellow
        
        # Alternative: Use pandoc if available
        try {
            pandoc --version | Out-Null
            Write-Host "✅ Pandoc found - using pandoc for conversion" -ForegroundColor Green
            
            pandoc "$InputFile" -o "$OutputFile" --reference-doc=template.docx
            Write-Host "✅ Document converted successfully using Pandoc!" -ForegroundColor Green
            Write-Host "📄 Output file: $OutputFile" -ForegroundColor Cyan
            return
        } catch {
            Write-Host "❌ Pandoc not found either." -ForegroundColor Red
            Write-Host "📋 Manual conversion instructions:" -ForegroundColor Yellow
            Write-Host "1. Open the markdown file: $InputFile" -ForegroundColor White
            Write-Host "2. Copy the content" -ForegroundColor White
            Write-Host "3. Paste into a new Word document" -ForegroundColor White
            Write-Host "4. Apply formatting as needed" -ForegroundColor White
            Write-Host "5. Save as: $OutputFile" -ForegroundColor White
            return
        }
    }

    # Create new Word document
    $Document = $Word.Documents.Add()
    
    # Read markdown content
    $Content = Get-Content $InputFile -Raw
    
    # Basic markdown to Word conversion
    Write-Host "🔄 Converting markdown content..." -ForegroundColor Yellow
    
    # Split content into lines for processing
    $Lines = $Content -split "`n"
    
    foreach ($Line in $Lines) {
        $Line = $Line.Trim()
        
        if ($Line -eq "") {
            # Empty line - add paragraph break
            $Document.Content.InsertParagraphAfter()
        }
        elseif ($Line.StartsWith("# ")) {
            # Heading 1
            $Text = $Line.Substring(2)
            $Paragraph = $Document.Content.Paragraphs.Add()
            $Paragraph.Range.Text = $Text
            $Paragraph.Range.Style = "Heading 1"
            $Paragraph.Range.InsertParagraphAfter()
        }
        elseif ($Line.StartsWith("## ")) {
            # Heading 2
            $Text = $Line.Substring(3)
            $Paragraph = $Document.Content.Paragraphs.Add()
            $Paragraph.Range.Text = $Text
            $Paragraph.Range.Style = "Heading 2"
            $Paragraph.Range.InsertParagraphAfter()
        }
        elseif ($Line.StartsWith("### ")) {
            # Heading 3
            $Text = $Line.Substring(4)
            $Paragraph = $Document.Content.Paragraphs.Add()
            $Paragraph.Range.Text = $Text
            $Paragraph.Range.Style = "Heading 3"
            $Paragraph.Range.InsertParagraphAfter()
        }
        elseif ($Line.StartsWith("```")) {
            # Code block - skip for now, handle in next iteration
            continue
        }
        elseif ($Line.StartsWith("| ")) {
            # Table row - basic table handling
            $Cells = $Line -split "\|" | Where-Object { $_.Trim() -ne "" }
            # Add table logic here if needed
            $Paragraph = $Document.Content.Paragraphs.Add()
            $Paragraph.Range.Text = $Line
            $Paragraph.Range.Font.Name = "Courier New"
            $Paragraph.Range.InsertParagraphAfter()
        }
        else {
            # Regular paragraph
            if ($Line -ne "") {
                $Paragraph = $Document.Content.Paragraphs.Add()
                $Paragraph.Range.Text = $Line
                $Paragraph.Range.InsertParagraphAfter()
            }
        }
    }
    
    # Format the document
    Write-Host "🎨 Applying formatting..." -ForegroundColor Yellow
    
    # Set document properties
    $Document.BuiltInDocumentProperties.Item("Title") = "Versapay to SAP FI Mapping Documentation"
    $Document.BuiltInDocumentProperties.Item("Author") = "SAP Integration Team"
    $Document.BuiltInDocumentProperties.Item("Subject") = "Field Mapping and Data Conversion Documentation"
    
    # Save the document
    Write-Host "💾 Saving document..." -ForegroundColor Yellow
    $Document.SaveAs([ref]$OutputFile)
    $Document.Close()
    $Word.Quit()
    
    # Release COM objects
    [System.Runtime.Interopservices.Marshal]::ReleaseComObject($Document) | Out-Null
    [System.Runtime.Interopservices.Marshal]::ReleaseComObject($Word) | Out-Null
    [System.GC]::Collect()
    [System.GC]::WaitForPendingFinalizers()
    
    Write-Host "✅ Word document created successfully!" -ForegroundColor Green
    Write-Host "📄 Output file: $OutputFile" -ForegroundColor Cyan
    Write-Host "📋 Document contains:" -ForegroundColor Yellow
    Write-Host "   - Complete field mapping documentation" -ForegroundColor White
    Write-Host "   - String token parsing methods" -ForegroundColor White
    Write-Host "   - SAP table query structures" -ForegroundColor White
    Write-Host "   - FI document generation logic" -ForegroundColor White
    Write-Host "   - Data conversion examples" -ForegroundColor White

} catch {
    Write-Host "❌ Error creating Word document: $($_.Exception.Message)" -ForegroundColor Red
    
    Write-Host "`n📋 Alternative Options:" -ForegroundColor Yellow
    Write-Host "1. Install Pandoc: https://pandoc.org/installing.html" -ForegroundColor White
    Write-Host "2. Use online markdown to Word converters" -ForegroundColor White
    Write-Host "3. Copy content manually from: $InputFile" -ForegroundColor White
    Write-Host "4. Use VS Code with Word export extensions" -ForegroundColor White
}

Write-Host "`n🎉 Process completed!" -ForegroundColor Green
