# PowerShell Build Script for SAP FI Versapay Integration
# Usage: .\scripts\build.ps1 [environment]
# Environments: dev, staging, prod

param(
    [Parameter(Mandatory=$false)]
    [ValidateSet("dev", "staging", "prod")]
    [string]$Environment = "dev"
)

Write-Host "🚀 Building SAP FI Versapay Integration - Environment: $Environment" -ForegroundColor Green

# Set error action preference
$ErrorActionPreference = "Stop"

try {
    # Check if Docker is running
    Write-Host "📋 Checking Docker status..." -ForegroundColor Yellow
    docker version | Out-Null
    if ($LASTEXITCODE -ne 0) {
        throw "Docker is not running. Please start Docker Desktop."
    }

    # Determine docker-compose file based on environment
    $ComposeFile = switch ($Environment) {
        "dev" { "docker-compose.dev.yml" }
        "staging" { "docker-compose.non-prod.yml" }
        "prod" { "docker-compose.prod.yml" }
    }

    Write-Host "📦 Using compose file: $ComposeFile" -ForegroundColor Cyan

    # Check if compose file exists
    if (-not (Test-Path $ComposeFile)) {
        throw "Docker compose file '$ComposeFile' not found."
    }

    # Check if config file exists
    $ConfigFile = "aconfig/config.$($Environment -eq 'staging' ? 'non-production' : $Environment).env"
    if (-not (Test-Path $ConfigFile)) {
        Write-Warning "⚠️  Configuration file '$ConfigFile' not found. Creating from template..."
        
        # Create config directory if it doesn't exist
        if (-not (Test-Path "aconfig")) {
            New-Item -ItemType Directory -Path "aconfig" -Force
        }
        
        # Copy template
        Copy-Item ".env.example" $ConfigFile
        Write-Host "📝 Please edit '$ConfigFile' with your actual configuration values." -ForegroundColor Yellow
        Write-Host "⏸️  Build paused. Press any key to continue after editing the config file..." -ForegroundColor Yellow
        $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
    }

    # Clean up previous containers
    Write-Host "🧹 Cleaning up previous containers..." -ForegroundColor Yellow
    docker-compose -f $ComposeFile down --volumes --remove-orphans 2>$null

    # Build and start services
    Write-Host "🔨 Building and starting services..." -ForegroundColor Green
    docker-compose -f $ComposeFile up --build -d

    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Build completed successfully!" -ForegroundColor Green
        Write-Host "📊 Container status:" -ForegroundColor Cyan
        docker-compose -f $ComposeFile ps
        
        Write-Host "`n📋 Useful commands:" -ForegroundColor Yellow
        Write-Host "  View logs: docker-compose -f $ComposeFile logs -f" -ForegroundColor White
        Write-Host "  Stop services: docker-compose -f $ComposeFile down" -ForegroundColor White
        Write-Host "  Restart: docker-compose -f $ComposeFile restart" -ForegroundColor White
    } else {
        throw "Build failed with exit code $LASTEXITCODE"
    }

} catch {
    Write-Host "❌ Build failed: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

Write-Host "🎉 Build process completed!" -ForegroundColor Green
