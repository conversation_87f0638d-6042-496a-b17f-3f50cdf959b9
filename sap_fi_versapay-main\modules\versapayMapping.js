import "dotenv/config";
import noderfc from "node-rfc";
import { versapayPayment } from "./db.js";

const rfcClient = noderfc.Client;

export default class sapClient {
    // SAP Client
    #abapClient

    // Constructor Method
    constructor(abap) {
        // create new sap-client 
        this.#abapClient = new rfcClient(abap);
    }

    versapayPayentPosting({
        payment_reference,
        customer_identifier,
        payment_transaction_fee,
        payment_amounts
    }) {
        return new Promise(async (resolve, reject) => {
            try {
                // Prepare final Payment Amounts
                const {
                    invoice_payments,
                    invoices,
                    companies
                } = this.#invoicePaymentCalculation({ payment_amounts, payment_transaction_fee });
                // Query SAP for default GL & Customer Profit Center
                // Get Table FAGL_T8A30 : General Ledger 0000100012: Default Profit Center
                const gl100012 = "0000100012";
                const gl440020 = "0000440020";
                const t8a30 = await this.#get_fagl_t8a30({ gl100012, companies });
                const knb1 = await this.#get_knb1({ customer_identifier, companies });
                const faglflexa = await this.#get_faglflexa({ invoices, knb1 });
                // Prepare final SAP Payload
                const sap_fi_docs = this.#CreateSapFiDoc({
                    invoice_payments,
                    t8a30,
                    knb1,
                    faglflexa,
                    gl440020
                });
                await versapayPayment.findOneAndUpdate(
                    { _id: payment_reference },
                    {
                        $set: {
                            invoice_payments,
                            sap_fi_docs,
                            companies,
                            invoices,
                            t8a30,
                            knb1,
                            faglflexa
                        }
                    },
                    { upsert: true, new: true }
                );

                resolve(sap_fi_docs);

            } catch (error) {
                const nError = new Error(error, { payment_reference });
                nError.retryAfter = 10;
                reject(nError);
            }
        })
    }

    #invoicePaymentCalculation({ payment_amounts, payment_transaction_fee }) {
        const invoices = [];
        let invoice_payments = [];
        let companies = {};

        // Invoice payments - line Items
        payment_amounts.forEach(payment => {
            let {
                payment_reference,
                customer_identifier,
                date,
                invoice_number,
                amount,
                payment_method,
                short_pay_reason,
                invoice_external_id, // Versapay Internal Configuration: MAPPED Discount from File
                invoice_amount,
                invoice_currency,
                invoice_internal_number,
                payment_note
            } = payment;

            // Retrieve Company Code
            let startPos = 0;
            let endPos = 4;
            const compCode = invoice_internal_number.slice(startPos, endPos);   //Company 4 Char

            // Retrieve Fiscal Year
            startPos = (endPos + invoice_number.length);
            endPos = (startPos + 4);
            const fiscalYear = invoice_internal_number.slice(startPos, endPos); //Fiscal Year 4 Char

            // Retrieve Invoice Document line Item
            startPos = endPos;
            endPos = (startPos + 3);
            const itemNum = invoice_internal_number.slice(startPos, endPos);    //Forth 3 Char

            // Item Text Concatinated for Versapay
            const itemText = `${invoice_number} ${short_pay_reason || ""} ${payment_note || ""}`;

            // Convert to Number            
            amount = Number(amount);                           // Customer Paid Amount      
            invoice_amount = Number(invoice_amount);           // Total Invoice Amount
            invoice_external_id = Number(invoice_external_id); // Customer Discount

            // Versapay Regular Payment
            // SAP Invoice amount Calculation, Overpay & Short pay // Number.parseFloat(total_amount).toFixed(2)
            const sap_invoice_amount = Number.parseFloat(invoice_amount - invoice_external_id).toFixed(2);

            // Balance Amount to calculate Exact , Less or Over Pay
            const balance_amount = Number.parseFloat(amount - sap_invoice_amount).toFixed(2);

            // Prepare payment Documents
            invoice_payments.push({
                payment_reference,
                payment_method,
                customer_identifier,
                date,
                compCode,
                invoice_number: invoice_number.padStart(10, '0'),
                fiscalYear,
                itemNum,
                payment_transaction_fee: "0.00",
                amount: Number.parseFloat(amount).toFixed(2),
                invoice_amount: Number.parseFloat(invoice_amount).toFixed(2),
                invoice_external_id: Number.parseFloat(invoice_external_id).toFixed(2),
                sap_invoice_amount: Number.parseFloat(sap_invoice_amount).toFixed(2),
                balance_amount: Number.parseFloat(balance_amount).toFixed(2),
                invoice_currency: invoice_currency.toUpperCase(),
                itemText
            });

            // Each Invoices will be different
            invoices.push({
                BELNR: invoice_number.padStart(10, '0'),
                BUKRS: compCode,
                GJAHR: fiscalYear,
                BUZEI: itemNum
            });

            // list Companies
            companies[compCode] = payment_reference;
        });

        // Sort based on Company Code and map the Credit-Card fees
        invoice_payments = invoice_payments.sort((a, b) => (a.compCode - b.compCode));
        invoice_payments[0].payment_transaction_fee = payment_transaction_fee;

        return {
            invoice_payments,
            invoices,
            companies
        }
    }

    async #get_fagl_t8a30({ gl100012, companies }) {
        return new Promise(async (resolve, reject) => {
            try {
                let options_t8a30 = [];
                Object.keys(companies).forEach((comp, i) => {
                    if ((i + 1) === 1) {
                        options_t8a30.push({ TEXT: `( ` });
                        options_t8a30.push({ TEXT: `BUKRS = '${comp}' ` });
                    } else {
                        options_t8a30.push({ TEXT: `OR BUKRS = '${comp}' ` });
                    }
                    if ((i + 1) === Object.keys(companies).length) {
                        options_t8a30.push({ TEXT: `) ` });
                    }
                });
                options_t8a30.push({ TEXT: `AND KONTO_VON = '${gl100012}'` });
                //Retrieve Table FAGL_T8A30
                await this.#abapClient.open();
                const { DATA: t8a30 } = await this.#abapClient.call("RFC_READ_TABLE", {
                    QUERY_TABLE: "FAGL_T8A30",
                    DELIMITER: "|",
                    OPTIONS: options_t8a30
                });
                await this.#abapClient.close();
                // Prepare Final Output
                const t8a30_final = [];
                t8a30.forEach(e => {
                    // 310|1000|0000100012|0000100012|0010|0000901000|1000
                    const column = e.WA.split("|");
                    t8a30_final.push({
                        BUKRS: column[1],
                        KONTO_VON: column[2],
                        PRCTR: column[5],
                    })
                });

                resolve(t8a30_final);
            } catch (error) {
                const nError = new Error(error.message);
                nError.retryAfter = 10;
                reject(nError);
            }
        })
    }

    #get_knb1({ customer_identifier, companies }) {
        return new Promise(async (resolve, reject) => {
            try {
                let options_knb1 = [{ TEXT: `KUNNR = '${customer_identifier.padStart(10, '0')}' AND ` }];
                Object.keys(companies).forEach((comp, i) => {
                    if ((i + 1) === 1) {
                        options_knb1.push({ TEXT: `( ` })
                        options_knb1.push({ TEXT: `BUKRS = '${comp}' ` })
                    } else {
                        options_knb1.push({ TEXT: `OR BUKRS = '${comp}' ` })
                    }
                    if ((i + 1) === Object.keys(companies).length) {
                        options_knb1.push({ TEXT: `) ` })
                    }
                });
                options_knb1.push({ TEXT: `AND LOEVM = ''` });

                // Get Customer KNB1 Reconciliation Account in General Ledger
                await this.#abapClient.open();
                const { DATA: knb1 } = await this.#abapClient.call("RFC_READ_TABLE", {
                    QUERY_TABLE: "KNB1",
                    DELIMITER: "|",
                    OPTIONS: options_knb1,
                    FIELDS: [
                        { FIELDNAME: "KUNNR", OFFSET: "000000", LENGTH: "000010" },
                        { FIELDNAME: "BUKRS", OFFSET: "000011", LENGTH: "000004" },
                        { FIELDNAME: "AKONT", OFFSET: "000016", LENGTH: "000010" }
                    ]
                });
                await this.#abapClient.close();

                // Prepare Final Output
                const knb1_final = [];
                knb1.forEach(e => {
                    // "**********|1000|**********"
                    const column = e.WA.split("|");
                    knb1_final.push({
                        KUNNR: column[0],
                        BUKRS: column[1],
                        AKONT: column[2],
                    })
                });

                resolve(knb1_final);
            } catch (error) {
                const nError = new Error(error.message);
                nError.retryAfter = 10;
                reject(nError);
            }
        })

    }

    #get_faglflexa({ invoices, knb1 }) {
        return new Promise(async (resolve, reject) => {
            try {
                // Prepare Final Output
                const faglflexa_final = [];
                const options_faglflexa = [];

                if (knb1.length > 0) {
                    invoices.forEach((inv, i) => {
                        const { BUKRS, BELNR, GJAHR } = inv;
                        if ((i + 1) === 1) {
                            options_faglflexa.push({
                                TEXT: `( RYEAR = '${GJAHR}' AND DOCNR = '${BELNR}' `
                            })
                        } else {
                            options_faglflexa.push({
                                TEXT: `OR ( RYEAR = '${GJAHR}' AND DOCNR = '${BELNR}' `
                            })
                        }
                        const AKONT = knb1.find(f => f.BUKRS === BUKRS).AKONT;
                        if (AKONT) {
                            options_faglflexa.push({
                                TEXT: `AND RLDNR = '0L' AND RBUKRS = '${BUKRS}' AND RACCT = '${AKONT}' ) `
                            });
                        }
                    });
                    await this.#abapClient.open();
                    const { DATA: faglflexa } = await this.#abapClient.call("RFC_READ_TABLE", {
                        QUERY_TABLE: "FAGLFLEXA",
                        DELIMITER: "|",
                        OPTIONS: options_faglflexa,
                        FIELDS: [
                            { FIELDNAME: "RYEAR", OFFSET: "000000", LENGTH: "000004" },
                            { FIELDNAME: "DOCNR", OFFSET: "000005", LENGTH: "000010" },
                            { FIELDNAME: "RLDNR", OFFSET: "000016", LENGTH: "000002" },
                            { FIELDNAME: "RBUKRS", OFFSET: "000019", LENGTH: "000004" },
                            { FIELDNAME: "DOCLN", OFFSET: "000024", LENGTH: "000006" },
                            { FIELDNAME: "RACCT", OFFSET: "000031", LENGTH: "000010" },
                            { FIELDNAME: "PRCTR", OFFSET: "000042", LENGTH: "000010" }
                        ]
                    });
                    await this.#abapClient.close();

                    faglflexa.forEach(e => {
                        // "2019|0090000047|0L|1000|000001|**********|0000482000"
                        const column = e.WA.split("|");
                        faglflexa_final.push({
                            RYEAR: column[0],
                            DOCNR: column[1],
                            RBUKRS: column[3],
                            RACCT: column[5],
                            PRCTR: column[6]
                        })
                    });
                }

                resolve(faglflexa_final);

            } catch (error) {
                const nError = new Error(error.message);
                nError.retryAfter = 10;
                reject(nError);
            }
        })
    }

    #CreateSapFiDoc({
        invoice_payments,
        t8a30,
        knb1,
        faglflexa,
        gl440020
    }) {
        const fiDocs = [];
        invoice_payments.forEach((item, i) => {
            let {
                payment_reference,
                customer_identifier,
                date,
                compCode,
                invoice_number,
                fiscalYear,
                itemNum,
                amount,
                payment_transaction_fee,
                sap_invoice_amount,
                balance_amount,
                invoice_currency,
                itemText
            } = item;

            const id = `${payment_reference}${i}`;
            amount = Number(amount);
            payment_transaction_fee = Number(payment_transaction_fee);
            sap_invoice_amount = Number(sap_invoice_amount);
            balance_amount = Number(balance_amount);

            const it8a30 = t8a30.find(g => (g.BUKRS === compCode));
            const cknb1 = knb1.find(c => (c.BUKRS === compCode));
            const cfaglflexa = faglflexa.find(f => (
                f.RYEAR === fiscalYear &&
                f.DOCNR === invoice_number &&
                f.RBUKRS === compCode &&
                f.RACCT === cknb1.AKONT
            ));

            // Line Item Counter 
            let item_num = 1;

            // Final Posting Document
            const fiDoc = {
                Header: {
                    COMP_CODE: compCode,
                    DOC_TYPE: (balance_amount === 0.00) ? "DZ" : "AB",
                    DOC_DATE: (new Date()).toISOString().slice(0, 10).replace(/-/g, ""),
                    PSTNG_DATE: date.replaceAll("-", ""),
                    HEADER_TXT: "VERSAPAY",
                    REF_DOC_NO: payment_reference,
                    BUS_ACT: "RFBU"
                },

                Items: [{
                    Item_Number: item_num.toString(),
                    General_Ledger: {
                        GL_ACCOUNT: it8a30.KONTO_VON,
                        COMP_CODE: it8a30.BUKRS,
                        PROFIT_CTR: it8a30.PRCTR,
                        ITEM_TEXT: itemText
                    },
                    Currency: {
                        CURRENCY: invoice_currency,
                        CURRENCY_ISO: invoice_currency,
                        AMT_DOCCUR: Number.parseFloat(amount + payment_transaction_fee).toFixed(2)
                    }
                }]
            }

            // Over Pay
            if ((balance_amount > 0) || (payment_transaction_fee > 0)) {
                item_num = item_num + 1;
                fiDoc.Items.push({
                    Item_Number: item_num.toString(),
                    General_Ledger: {
                        GL_ACCOUNT: gl440020,
                        COMP_CODE: compCode,
                        PROFIT_CTR: cfaglflexa?.PRCTR ?? '', // pc942000,
                        ITEM_TEXT: itemText
                    },
                    Currency: {
                        CURRENCY: invoice_currency,
                        CURRENCY_ISO: invoice_currency,
                        AMT_DOCCUR: Number.parseFloat((((balance_amount > 0) ? balance_amount : 0) + payment_transaction_fee) * (-1)).toFixed(2)
                    }
                })
            }

            // Short Pay or Under pay
            if (balance_amount < 0) {
                item_num = item_num + 1;
                fiDoc.Items.push({
                    Item_Number: item_num.toString(),
                    Customer: {
                        CUSTOMER: customer_identifier.padStart(10, '0'),
                        COMP_CODE: compCode,
                        PROFIT_CTR: cfaglflexa?.PRCTR ?? '',
                        ITEM_TEXT: itemText
                    },
                    Currency: {
                        CURRENCY: invoice_currency,
                        CURRENCY_ISO: invoice_currency,
                        AMT_DOCCUR: Number.parseFloat(balance_amount * (-1)).toFixed(2)
                    }
                })
            }

            // Then Perform Customer Clearing
            item_num = item_num + 1;
            fiDoc.Items.push({
                Item_Number: item_num.toString(),
                Customer: {
                    CUSTOMER: customer_identifier.padStart(10, '0'),
                    COMP_CODE: compCode,
                    PROFIT_CTR: cfaglflexa?.PRCTR ?? '',
                    ITEM_TEXT: itemText
                },
                Currency: {
                    CURRENCY: invoice_currency,
                    CURRENCY_ISO: invoice_currency,
                    AMT_DOCCUR: Number.parseFloat(sap_invoice_amount * (-1)).toFixed(2)
                },
                CustomerClearing: [{
                    BELNR: invoice_number,
                    BUKRS: compCode,
                    GJAHR: fiscalYear,
                    BUZEI: itemNum
                }]
            });

            fiDocs.push({ id, fiDoc });
            // fiDocs.push({ key: (payment_reference + i), value: JSON.stringify(fiDoc) });
        });

        return fiDocs;
    }
}