import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import axios from 'axios';
import { versapayConnect, getChildrensPayments } from '../../modules/apiaxios.js';

// Mock axios
vi.mock('axios');
const mockedAxios = vi.mocked(axios);

describe('Versapay API Integration', () => {
    beforeEach(() => {
        vi.clearAllMocks();
        
        // Mock environment variables
        process.env.VERSAPAY_ENDPOINT = 'https://api.versapay.com';
        process.env.VERSAPAY_USERNAME = 'test_user';
        process.env.VERSAPAY_PASSWORD = 'test_pass';
        process.env.VERSAPAY_WHOAMI = 'test_element';
    });

    afterEach(() => {
        vi.restoreAllMocks();
    });

    describe('versapayConnect', () => {
        it('should successfully connect to Versapay API', async () => {
            const mockResponse = {
                status: 200,
                data: { success: true }
            };

            mockedAxios.create.mockReturnValue({
                get: vi.fn().mockResolvedValue(mockResponse)
            });

            const result = await versapayConnect();
            expect(result.status).toBe(200);
        });

        it('should handle connection errors', async () => {
            const mockError = new Error('Connection failed');
            
            mockedAxios.create.mockReturnValue({
                get: vi.fn().mockRejectedValue(mockError)
            });

            await expect(versapayConnect()).rejects.toThrow();
        });
    });

    describe('getChildrensPayments', () => {
        it('should fetch children payments successfully', async () => {
            const mockWhoamiResponse = {
                status: 200,
                data: {
                    whoami: {
                        test_element: {
                            jwt: 'mock_jwt_token'
                        }
                    }
                }
            };

            const mockPaymentResponse = {
                status: 200,
                data: {
                    payment_amounts: [
                        { invoice_number: 'INV001', amount: 100.00 }
                    ],
                    payment_tree: {
                        children: ['child1', 'child2']
                    }
                }
            };

            const mockChildResponse = {
                data: {
                    payment_method: 'Credit',
                    payment_amounts: [
                        { invoice_number: 'INV001', amount: 50.00 }
                    ]
                }
            };

            const mockAxiosInstance = {
                get: vi.fn()
                    .mockResolvedValueOnce(mockWhoamiResponse)
                    .mockResolvedValueOnce(mockPaymentResponse)
                    .mockResolvedValue(mockChildResponse)
            };

            mockedAxios.create.mockReturnValue(mockAxiosInstance);

            const result = await getChildrensPayments('test_reference');
            expect(Array.isArray(result)).toBe(true);
        });

        it('should handle API errors gracefully', async () => {
            const mockError = new Error('API Error');
            
            mockedAxios.create.mockReturnValue({
                get: vi.fn().mockRejectedValue(mockError)
            });

            await expect(getChildrensPayments('test_reference')).rejects.toThrow();
        });
    });
});
