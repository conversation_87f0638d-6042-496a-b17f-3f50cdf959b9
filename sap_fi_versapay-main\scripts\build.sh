#!/bin/bash
# Bash Build Script for SAP FI Versapay Integration
# Usage: ./scripts/build.sh [environment]
# Environments: dev, staging, prod

set -e

ENVIRONMENT=${1:-dev}

# Validate environment parameter
if [[ ! "$ENVIRONMENT" =~ ^(dev|staging|prod)$ ]]; then
    echo "❌ Invalid environment. Use: dev, staging, or prod"
    exit 1
fi

echo "🚀 Building SAP FI Versapay Integration - Environment: $ENVIRONMENT"

# Check if Docker is running
echo "📋 Checking Docker status..."
if ! docker version >/dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker."
    exit 1
fi

# Determine docker-compose file based on environment
case $ENVIRONMENT in
    "dev")
        COMPOSE_FILE="docker-compose.dev.yml"
        CONFIG_ENV="development"
        ;;
    "staging")
        COMPOSE_FILE="docker-compose.non-prod.yml"
        CONFIG_ENV="non-production"
        ;;
    "prod")
        COMPOSE_FILE="docker-compose.prod.yml"
        CONFIG_ENV="production"
        ;;
esac

echo "📦 Using compose file: $COMPOSE_FILE"

# Check if compose file exists
if [[ ! -f "$COMPOSE_FILE" ]]; then
    echo "❌ Docker compose file '$COMPOSE_FILE' not found."
    exit 1
fi

# Check if config file exists
CONFIG_FILE="aconfig/config.$CONFIG_ENV.env"
if [[ ! -f "$CONFIG_FILE" ]]; then
    echo "⚠️  Configuration file '$CONFIG_FILE' not found. Creating from template..."
    
    # Create config directory if it doesn't exist
    mkdir -p aconfig
    
    # Copy template
    cp .env.example "$CONFIG_FILE"
    echo "📝 Please edit '$CONFIG_FILE' with your actual configuration values."
    echo "⏸️  Build paused. Press any key to continue after editing the config file..."
    read -n 1 -s
fi

# Clean up previous containers
echo "🧹 Cleaning up previous containers..."
docker-compose -f "$COMPOSE_FILE" down --volumes --remove-orphans 2>/dev/null || true

# Build and start services
echo "🔨 Building and starting services..."
docker-compose -f "$COMPOSE_FILE" up --build -d

if [[ $? -eq 0 ]]; then
    echo "✅ Build completed successfully!"
    echo "📊 Container status:"
    docker-compose -f "$COMPOSE_FILE" ps
    
    echo ""
    echo "📋 Useful commands:"
    echo "  View logs: docker-compose -f $COMPOSE_FILE logs -f"
    echo "  Stop services: docker-compose -f $COMPOSE_FILE down"
    echo "  Restart: docker-compose -f $COMPOSE_FILE restart"
else
    echo "❌ Build failed"
    exit 1
fi

echo "🎉 Build process completed!"
