# Non-Production Environment Configuration
# SAP FI Versapay Integration

# MongoDB Configuration
MONGO_INITDB_ROOT_USERNAME=sapversapay_user
MONGO_INITDB_ROOT_PASSWORD=your_secure_mongo_password
MONGODB_HOST=your-mongo-host:27017

# SAP RFC Configuration
ASHOST=ud1-sv-sapeq1.matw.matthewsintl.com
SYSNR=00
CLIENT=300
LANGU=EN
SAPUSER=your_sap_qa_user
SAPPSW=your_sap_qa_password
SYSTEM=NQ1
RFC_TRACE=0

# Kafka Configuration
KAFKA_BROKER=dc6-sv-c3mft02.amer.schawk.com:9092,dc6-sv-c3mft02.amer.schawk.com:9093
KAFKA_CLIENTID=SAP.GL.POSTING
KAFKA_GROUPID=QA-SAP-GL-POST-VERSAPAY
KAFKA_TOPIC_RECEIVED=QA.SAPGL.RECEIVED
KAFKA_TOPIC_VERSAPAY=versapay_payment_qa
KAFKAJS_NO_PARTITIONER_WARNING=1

# Versapay API Configuration
VERSAPAY_ENDPOINT=https://api-sandbox.versapay.com
VERSAPAY_USERNAME=your_versapay_qa_username
VERSAPAY_PASSWORD=your_versapay_qa_password
VERSAPAY_WHOAMI=your_whoami_element

# Application Configuration
NODE_ENV=staging
LOG_LEVEL=info
