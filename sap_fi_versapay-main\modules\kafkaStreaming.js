import "dotenv/config";
import { Kafka, CompressionTypes, logLevel } from 'kafkajs';
import { getChildrensPayments } from "./apiaxios.js";
import sapClient from "./versapayMapping.js"
import { kafkaRetry } from "./db.js";

const {
    KAFKA_CLIENTID: clientId,
    KAFKA_GROUPID: groupId,
    KAFKA_TOPIC_VERSAPAY: topic_versapay,
    KAFKA_TOPIC_RECEIVED: qa_sapgl_received,
    ASHOST: ashost,
    SYSNR: sysnr,
    CLIENT: client,
    LANGU: lang,
    SAPUSER: user,
    SAPPSW: passwd,
    SYSTEM: dest
} = process.env;

const brokers = process.env.KAFKA_BROKER.split(',');
const fromBeginning = true;

const topics = [topic_versapay];
const kafka = new Kafka({
    logLevel: logLevel.NOTHING,
    brokers,
    clientId
});

// Producer
const producer = kafka.producer();
const kafkaTopicProducer = ({ topic, message }) => {
    return new Promise(async (resolve, reject) => {
        try {
            await producer.connect();
            await producer.send({
                topic,
                messages: message,
                compression: CompressionTypes.GZIP
            });
            await producer.disconnect();
            resolve({});
        } catch (error) {
            await producer.disconnect();
            resolve({});
        }
    })
}

// Utility function to simulate delay
const delay = (ms) => new Promise((resolve) => setTimeout(resolve, ms));

// Consumers
const consumer = kafka.consumer({ groupId });
const runConsumer = async () => {
    await consumer.connect();
    await consumer.subscribe({ topics, fromBeginning });
    await consumer.run({
        eachBatch: async ({
            batch,
            resolveOffset,
            heartbeat,
            commitOffsetsIfNecessary,

            uncommittedOffsets,
            isRunning,
            isStale,
            pause

        }) => {
            for (let message of batch.messages) {
                if (!isRunning() || isStale()) break;
                // convert Date-Time to EST
                const estDateTime = getCurrentDateTime();

                const topic = batch.topic;
                const { key, value, headers } = message;
                const versapay = JSON.parse(value.toString()); // Versapay Payment Received
                const {
                    payment_reference,
                    payment_method,
                    customer_identifier,
                    payment_transaction_fee,
                    payment_amounts
                } = versapay;

                console.log(`[${estDateTime}][${topic}][${payment_reference}][${payment_method}][${qa_sapgl_received}]`);

                // Exclude payment_method = 'Credit' or 'Discount'
                if (!['Credit', 'Discount'].includes(payment_method)) {
                    try {
                        // Get Children Payment Records from Versapay
                        const all_payment_amounts = await getChildrensPayments(payment_reference);
                        // VAlidating with SAP
                        const sapFi = new sapClient({ dest, ashost, sysnr, client, lang, user, passwd });
                      

                        const sapFiDocs = await sapFi.versapayPayentPosting({
                            payment_reference,

                            customer_identifier,
                            payment_transaction_fee,
                            payment_amounts: all_payment_amounts
                        });
                        // Posting to Generic Kafka Topic for Account Document Posting in SAP
                        await kafkaTopicProducer({
                            topic: qa_sapgl_received,
                            message: sapFiDocs.map(o => ({ key: o.id, value: JSON.stringify(o.fiDoc) }))
                        });

                    } catch (error) {
                        console.log('catch-error : ', error.message);
                        const { retry } = await kafkaRetry.findOneAndUpdate(
                            { _id: payment_reference },
                            {
                                $inc: { retry: 1 },
                                $set: { errormessage: error?.message }
                            },
                            { upsert: true, new: true }
                        );
                        console.log('catch-error-retry : ', retry);
                        if (retry < 4) {
                            console.log(`Error: (${retry}) ${error.message}`);
                            throw error;
                        }
                    }
                }
                // Resolve Offsets
                resolveOffset(message.offset);
                await heartbeat();
            }
        }
    })

}

const getCurrentDateTime = () => {
    return new Intl.DateTimeFormat('en-US', {
        timeZone: 'America/New_York',
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        fractionalSecondDigits: 3 // for milliseconds
    }).format(new Date());
    // (new Date()).toLocaleString("en-US", { timeZone: "America/New_York" });
}

const errorTypes = ['unhandledRejection', 'uncaughtException']
const signalTraps = ['SIGTERM', 'SIGINT', 'SIGUSR2']

errorTypes.forEach(type => {
    process.on(type, async e => {
        try {
            console.log(`process.on ${type}`)
            console.error(e)
            await consumer.disconnect()
            process.exit(0)
        } catch (_) {
            process.exit(1)
        }
    })
})

signalTraps.forEach(type => {
    process.once(type, async () => {
        try {
            await consumer.disconnect()
        } finally {
            process.kill(process.pid, type)
        }
    })
})

export default runConsumer;