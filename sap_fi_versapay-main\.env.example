# MongoDB Configuration
MONGO_INITDB_ROOT_USERNAME=your_mongo_username
MONGO_INITDB_ROOT_PASSWORD=your_mongo_password
MONGODB_HOST=localhost:27017

# SAP RFC Configuration
ASHOST=your_sap_host
SYSNR=00
CLIENT=300
LANGU=EN
SAPUSER=your_sap_username
SAPPSW=your_sap_password
SYSTEM=DEV
RFC_TRACE=0

# Kafka Configuration
KAFKA_BROKER=localhost:9092
KAFKA_CLIENTID=SAP.GL.POSTING
KAFKA_GROUPID=DEV-SAP-GL-POST-VERSAPAY
KAFKA_TOPIC_RECEIVED=DEV.SAPGL.RECEIVED
KAFKA_TOPIC_VERSAPAY=versapay_payment_dev
KAFKAJS_NO_PARTITIONER_WARNING=1

# Versapay API Configuration
VERSAPAY_ENDPOINT=https://api.versapay.com
VERSAPAY_WHOAMI=your_whoami_element
VERSAPAY_USERNAME=your_versapay_username
VERSAPAY_PASSWORD=your_versapay_password

# Application Configuration
NODE_ENV=development
LOG_LEVEL=debug
