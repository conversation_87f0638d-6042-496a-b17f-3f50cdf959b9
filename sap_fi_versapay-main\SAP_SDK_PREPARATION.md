# SAP SDK Node-RFC Installation Preparation Guide

## 🎯 Overview

This guide helps you prepare for the SAP SDK node-rfc software installation and provides steps to integrate it with the SAP FI Versapay application.

## 📋 Pre-Installation Checklist

### System Requirements
- [ ] **Windows 10/11** (64-bit) or Windows Server 2019/2022
- [ ] **Visual Studio Build Tools 2019/2022** (C++ build tools)
- [ ] **Python 3.8-3.11** (for node-gyp compilation)
- [ ] **Node.js 18.20.4+** (LTS version recommended)
- [ ] **Administrator privileges** for SDK installation

### SAP System Information Needed
- [ ] **SAP Application Server Host** (ASHOST)
- [ ] **System Number** (SYSNR)
- [ ] **Client Number** (CLIENT)
- [ ] **RFC User Credentials** (Username/Password)
- [ ] **SAP System ID** (SID)
- [ ] **Network connectivity** to SAP system (ports 33XX, 32XX)

## 🔧 Installation Steps (When SDK Arrives)

### Step 1: Install SAP NetWeaver RFC SDK
```powershell
# 1. Extract SAP NetWeaver RFC SDK to a directory (e.g., C:\SAP\NWRFCSDK)
# 2. Set environment variables
$env:SAPNWRFC_HOME = "C:\SAP\NWRFCSDK"
$env:PATH += ";C:\SAP\NWRFCSDK\lib"

# 3. Verify installation
Test-Path "$env:SAPNWRFC_HOME\lib\sapnwrfc.dll"
```

### Step 2: Configure System Environment Variables
```powershell
# Add to System PATH (permanent)
[Environment]::SetEnvironmentVariable("SAPNWRFC_HOME", "C:\SAP\NWRFCSDK", "Machine")
[Environment]::SetEnvironmentVariable("PATH", $env:PATH + ";C:\SAP\NWRFCSDK\lib", "Machine")

# Restart PowerShell/Command Prompt after setting environment variables
```

### Step 3: Install Node.js Dependencies
```bash
# Navigate to project directory
cd sap_fi_versapay-main

# Clean previous installation attempts
rm -rf node_modules package-lock.json

# Install with proper SDK path
npm config set target_arch x64
npm config set target_platform win32
npm config set python "C:\Python311\python.exe"

# Install dependencies
npm install
```

### Step 4: Test SAP RFC Connection
```javascript
// Create test file: test-sap-connection.js
import noderfc from "node-rfc";

const connectionParams = {
    ashost: process.env.ASHOST,
    sysnr: process.env.SYSNR,
    client: process.env.CLIENT,
    user: process.env.SAPUSER,
    passwd: process.env.SAPPSW,
    lang: process.env.LANGU || 'EN'
};

async function testConnection() {
    const client = new noderfc.Client(connectionParams);
    
    try {
        await client.open();
        console.log("✅ SAP RFC Connection successful!");
        
        // Test a simple RFC call
        const result = await client.call("RFC_SYSTEM_INFO");
        console.log("📊 SAP System Info:", result);
        
        await client.close();
    } catch (error) {
        console.error("❌ SAP RFC Connection failed:", error.message);
    }
}

testConnection();
```

## 🔍 Troubleshooting Common Issues

### Issue 1: DLL Not Found
```powershell
# Verify SAP RFC SDK installation
Test-Path "C:\SAP\NWRFCSDK\lib\sapnwrfc.dll"

# Check environment variables
echo $env:SAPNWRFC_HOME
echo $env:PATH
```

### Issue 2: Python Build Errors
```bash
# Install Python build tools
npm install --global windows-build-tools

# Or install Visual Studio Build Tools manually
# Download from: https://visualstudio.microsoft.com/downloads/#build-tools-for-visual-studio-2022
```

### Issue 3: Node-gyp Compilation Errors
```bash
# Clear npm cache
npm cache clean --force

# Rebuild node modules
npm rebuild

# Install with verbose logging
npm install --verbose
```

### Issue 4: SAP Connection Errors
```javascript
// Common connection issues and solutions:

// 1. Network connectivity
// Test: telnet <SAP_HOST> 33<SYSNR>

// 2. RFC user permissions
// Ensure user has RFC_USER profile in SAP

// 3. Client/System parameters
// Verify ASHOST, SYSNR, CLIENT values

// 4. Firewall/Security
// Check Windows Firewall and corporate firewalls
```

## 🧪 Testing Strategy

### Unit Tests
```bash
# Run existing tests (should work without SAP connection)
npm test

# Run tests with SAP connection (after SDK installation)
npm run test:integration
```

### Integration Testing
```bash
# Test individual components
node test-sap-connection.js
node test-versapay-api.js
node test-kafka-connection.js
node test-mongodb-connection.js
```

### End-to-End Testing
```bash
# Start application in development mode
npm run dev

# Monitor logs for successful connections
# Check MongoDB for processed records
# Verify Kafka message consumption
```

## 📦 Alternative Solutions (If SDK Installation Delayed)

### Option 1: Use Docker with Pre-built Image
```bash
# The existing Dockerfile uses a base image with SAP RFC libraries
# This bypasses local SDK installation requirements
docker-compose -f docker-compose.dev.yml up --build
```

### Option 2: SAP REST API Migration
```javascript
// Consider migrating to SAP OData/REST APIs
// This eliminates the need for RFC SDK entirely

import axios from 'axios';

const sapRestClient = axios.create({
    baseURL: 'https://your-sap-gateway.com/sap/opu/odata/sap/',
    auth: {
        username: process.env.SAP_USERNAME,
        password: process.env.SAP_PASSWORD
    }
});

// Example: Get customer data via REST instead of RFC
async function getCustomerData(customerId) {
    const response = await sapRestClient.get(`CustomerSet('${customerId}')`);
    return response.data;
}
```

### Option 3: SAP Cloud Connector
```javascript
// Use SAP Cloud Platform connectivity
// Provides secure tunnel to on-premise SAP systems
// Eliminates need for direct RFC connections
```

## 📞 Support Contacts

When the SAP SDK arrives, contact these teams if you encounter issues:

1. **SAP Basis Team** - For SAP system connectivity and RFC user setup
2. **Network Team** - For firewall and network connectivity issues  
3. **Development Team** - For application integration and testing
4. **Infrastructure Team** - For server and environment setup

## 📋 Post-Installation Verification

After successful SDK installation:

- [ ] SAP RFC connection test passes
- [ ] Application starts without errors
- [ ] Kafka consumer connects successfully
- [ ] MongoDB connection established
- [ ] Versapay API authentication works
- [ ] End-to-end payment processing completes
- [ ] All unit and integration tests pass

## 🎉 Success Criteria

The installation is successful when:
1. `npm install` completes without errors
2. `node test-sap-connection.js` shows successful connection
3. Application starts and processes messages
4. All external system connections are established
5. Payment workflow completes end-to-end

---

**Next Steps**: Once SAP SDK is installed, run through this checklist and contact the development team for any issues.
